
// Show add tournament modal
function showAddTournamentModal() {
    $('#addTournamentModal').modal('show');
    // Auto-focus the native select when the modal is shown
    $('#addTournamentModal').on('shown.bs.modal', function () {
        $('#id_pais_modal').trigger('focus');
    });
}

// Add selected tournament from modal
function addSelectedTournamentFromModal() {
    const selectedPaisId = document.getElementById('id_pais_modal').value;
    if (selectedPaisId) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'epartido_probabilidades';
        
        // Add hidden fields
        const idPartidoField = document.createElement('input');
        idPartidoField.type = 'hidden';
        idPartidoField.name = 'id_partido';
        idPartidoField.value = document.getElementById('id_partido').value;
        form.appendChild(idPartidoField);
        
        const tabSelectedField = document.createElement('input');
        tabSelectedField.type = 'hidden';
        tabSelectedField.name = 'tabselected';
        tabSelectedField.value = document.getElementById('tabselected').value;
        form.appendChild(tabSelectedField);
        
        const tabSelectedCornersField = document.createElement('input');
        tabSelectedCornersField.type = 'hidden';
        tabSelectedCornersField.name = 'tabselected_corners';
        tabSelectedCornersField.value = document.getElementById('tabselected_corners').value;
        form.appendChild(tabSelectedCornersField);
        
        const idPaisField = document.createElement('input');
        idPaisField.type = 'hidden';
        idPaisField.name = 'id_pais';
        idPaisField.value = selectedPaisId;
        form.appendChild(idPaisField);
        
        document.body.appendChild(form);
        form.submit();
    } else {
        alert('Please select a tournament to add.');
    }
}

// Show create tournament modal
function showCreateTournamentModal() {
    $('#createTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#createTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_create').focus();
    });
}

// Show edit tournament modal
function openEditTournamentModal(tournamentId, tournamentName) {
    document.getElementById('tournament_id_edit').value = tournamentId;
    document.getElementById('tournament_name_edit').value = tournamentName;
    $('#editTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#editTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_edit').focus().select();
    });
}

// Handle create tournament form submission
document.getElementById('createTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_create_tournament', '1');
    formData.append('tournament_name', document.getElementById('tournament_name_create').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#createTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error creating tournament. Please try again.');
        });
});

// Handle edit tournament form submission
document.getElementById('editTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_edit_tournament', '1');
    formData.append('tournament_id', document.getElementById('tournament_id_edit').value);
    formData.append('tournament_name', document.getElementById('tournament_name_edit').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#editTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating tournament. Please try again.');
        });
});

// Historical filtering functionality will be handled inline in the PHP view file
// since it needs access to PHP variables for team names

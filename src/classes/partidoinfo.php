<?php

require_once __ROOT__ . '/src/classes/pais.php';

class PartidoInfo
{
	public string  $id;
	public string  $fecha;
	public string  $status;
	public string  $home;
	public string  $away;
	public int     $homecorners;
	public int     $awaycorners;
	public int     $homegoals;
	public int     $awaygoals;
	public int     $homeshots;
	public int     $awayshots;
	public int     $homeshotstarget;
	public int     $awayshotstarget;
	public int     $homeposession;
	public int     $awayposession;
	public ?string $fecha_upload;
	public Pais    $pais;
	public string  $season;
	public string  $team;
	public float   $corners_total;
	public float   $goals_total;
	public string  $nom_pais;
	public string  $bd_table           = 'partidos_info';
	public string  $bd_alias           = 'parin';
	public string  $bd_id              = 'id_partido_info';
	public string  $bd_fecha           = 'fecha';
	public string  $bd_status          = 'status';
	public string  $bd_home            = 'home';
	public string  $bd_away            = 'away';
	public string  $bd_homecorners     = 'home_corners';
	public string  $bd_awaycorners     = 'away_corners';
	public string  $bd_homegoals       = 'home_goals';
	public string  $bd_awaygoals       = 'away_goals';
	public string  $bd_idpais          = 'id_pais';
	public string  $bd_season          = 'season';
	public string  $bd_homeshots       = 'homeshots';
	public string  $bd_awayshots       = 'awayshots';
	public string  $bd_homeshotstarget = 'homeshotstarget';
	public string  $bd_awayshotstarget = 'awayshotstarget';
	public string  $bd_homeposession   = 'homeposession';
	public string  $bd_awayposession   = 'awayposession';
	public string  $bd_fecha_upload    = 'fecha_upload';
	const NUMERO_CRITERIOS             = 6;
	const VALUETOYELLOW                = 51;
	const VALUETOGREEN                 = 60;
	const VALUETOGREENSTRONG           = 70;
	const BGTOGREEN                    = 'bg-success';
	const BGTOGREENSTRONG              = 'bg-green-800';
	const BGTOYELLOW                   = 'bg-warning';
	const DEFAULT_TOTALCORNERS_MASDE   = 9.5;
	const DEFAULT_TOTALCORNERS_MENOSDE = 10.5;
	const DEFAULT_MASXCORNERSHOME      = 5.5;
	const DEFAULT_MENOSXCORNERSHOME    = 5.5;
	const DEFAULT_MASXCORNERSAWAY      = 5.5;
	const DEFAULT_MENOSXCORNERSAWAY    = 5.5;
	const DEFAULT_TOTALGOLES_MASDE     = 1.5;
	const DEFAULT_TOTALGOLES_MENOSDE   = 2.5;
	const DEFAULT_CORNERSHOME_MASDE    = 5.5;
	const DEFAULT_CORNERSHOME_MENOSDE  = 5.5;
	const DEFAULT_CORNERSAWAY_MASDE    = 5.5;
	const DEFAULT_CORNERSAWAY_MENOSDE  = 5.5;
	const DEFAULT_GOLES_HOME_MASDE     = 0.5;
	const DEFAULT_GOLES_HOME_MENOSDE   = 2.5;
	const DEFAULT_GOLES_AWAY_MASDE     = 0.5;
	const DEFAULT_GOLES_AWAY_MENOSDE   = 2.5;
	const MIN_TOTAL_CORNERS            = 6;
	const MAX_TOTAL_CORNERS            = 13;
	const MIN_CORNERS_PERTEAM          = 1;
	const MAX_CORNERS_PERTEAM          = 9;
	const MIN_TOTAL_GOLES              = 0;
	const MAX_TOTAL_GOLES              = 5;
	const MIN_GOLES_PERTEAM            = 0;
	const MAX_GOLES_PERTEAM            = 3;
	
	function __construct()
	{
		$this->id              = '';
		$this->fecha           = '';
		$this->status          = '';
		$this->home            = '';
		$this->away            = '';
		$this->homecorners     = 0;
		$this->awaycorners     = 0;
		$this->homegoals       = 0;
		$this->awaygoals       = 0;
		$this->pais            = new Pais;
		$this->pais->id        = '';
		$this->season          = '';
		$this->homeshots       = 0;
		$this->awayshots       = 0;
		$this->homeshotstarget = 0;
		$this->awayshotstarget = 0;
		$this->homeposession   = 0;
		$this->awayposession   = 0;
		$this->fecha_upload    = '';
		$this->corners_total   = 0;
		$this->goals_total     = 0;
		$this->nom_pais        = '';
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                  = new self;
			$objeto->id              = desordena($resultado[$cq->bd_id]);
			$objeto->fecha           = $resultado[$cq->bd_fecha];
			$objeto->status          = $resultado[$cq->bd_status];
			$objeto->home            = $resultado[$cq->bd_home];
			$objeto->away            = $resultado[$cq->bd_away];
			$objeto->homecorners     = $resultado[$cq->bd_homecorners];
			$objeto->awaycorners     = $resultado[$cq->bd_awaycorners];
			$objeto->corners_total   = $objeto->homecorners + $objeto->awaycorners;
			$objeto->homegoals       = $resultado[$cq->bd_homegoals];
			$objeto->awaygoals       = $resultado[$cq->bd_awaygoals];
			$objeto->goals_total     = $objeto->homegoals + $objeto->awaygoals;
			$objeto->season          = $resultado[$cq->bd_season];
			$objeto->homeshots       = $resultado[$cq->bd_homeshots];
			$objeto->awayshots       = $resultado[$cq->bd_awayshots];
			$objeto->homeshotstarget = $resultado[$cq->bd_homeshotstarget];
			$objeto->awayshotstarget = $resultado[$cq->bd_awayshotstarget];
			$objeto->homeposession   = $resultado[$cq->bd_homeposession];
			$objeto->awayposession   = $resultado[$cq->bd_awayposession];
			$objeto->fecha_upload    = $resultado[$cq->bd_fecha_upload];
			$objeto->nom_pais        = (isset($resultado['nom_pais'])) ? $resultado['nom_pais'] : "";
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function constructArrayCriterios(): array
	{
		try {
			$listado = array();
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				$listado[$i] = 0;
			}
			
			return $listado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function constructArrayRangos(): array
	{
		try {
			$listado = array();
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				$listado[$i]['masde']     = 0;
				$listado[$i]['menosde']   = 0;
				$listado[$i]['yes']       = 0;
				$listado[$i]['no']        = 0;
				$listado[$i]['home']      = 0;
				$listado[$i]['away']      = 0;
				$listado[$i]['home_wins'] = 0;
				$listado[$i]['away_wins'] = 0;
			}
			
			return $listado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function constructArrayRangosEmpty(): array
	{
		try {
			$listado = array();
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				$listado[$i] = 0;
			}
			
			return $listado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function construct_array($array): array
	{
		try {
			$listado = array();
			
			for ($i = 0; $i < count($array); $i++) {
				$listado[$i] = '';
			}
			
			return $listado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, $conexion): self
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_fechamax_byteam($team, $place, $conexion): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = " SELECT ";
			$query .= "  IFNULL(MAX($cqa.$cq->bd_fecha), '') ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_status = 'COMPLETE' ";
			
			if ($place == 'home') {
				$query .= "  AND $cqa.$cq->bd_home = :team ";
				
			} else {
				$query .= "  AND $cqa.$cq->bd_away = :team ";
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":team", $team);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return $resultado[0];
				
			} else {
				return '';
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_posession_vsall_query($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT  ";
			$query .= "  t.team ";
			$query .= "  , ROUND(AVG(t.posession), 2) as posession ";
			$query .= "FROM ( ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_home as team ";
			$query .= "    ,$cqa.$cq->bd_homeposession as posession  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= "  UNION  ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_away as team ";
			$query .= "    ,$cqa.$cq->bd_awayposession as posession  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= ") as t ";
			$query .= "GROUP BY  ";
			$query .= "  t.team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shots_vsall_query($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT  ";
			$query .= "  t.team ";
			$query .= "  , ROUND(AVG(t.shots), 2) as shots ";
			$query .= "FROM ( ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_home as team ";
			$query .= "    ,$cqa.$cq->bd_homeshots as shots  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= "  UNION  ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_away as team ";
			$query .= "    ,$cqa.$cq->bd_awayshots as shots  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= ") as t ";
			$query .= "GROUP BY  ";
			$query .= "  t.team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shotstarget_vsall_query($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT  ";
			$query .= "  t.team ";
			$query .= "  , ROUND(AVG(t.shotstarget), 2) as shotstarget ";
			$query .= "FROM ( ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_home as team ";
			$query .= "    ,$cqa.$cq->bd_homeshotstarget as shotstarget  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= "  UNION  ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_away as team ";
			$query .= "    ,$cqa.$cq->bd_awayshotstarget as shotstarget  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= ") as t ";
			$query .= "GROUP BY  ";
			$query .= "  t.team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_corners_vsall_query($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT  ";
			$query .= "  t.team ";
			$query .= "  , SUM(t.corners) as corners ";
			$query .= "  , ROUND(AVG(t.corners), 2) as avg_corners ";
			$query .= "FROM ( ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_home as team ";
			$query .= "    ,$cqa.$cq->bd_homecorners as corners  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= "  UNION  ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_away as team ";
			$query .= "    ,$cqa.$cq->bd_awaycorners as corners  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= ") as t ";
			$query .= "GROUP BY  ";
			$query .= "  t.team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_goals_vsall_query($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT  ";
			$query .= "  t.team ";
			$query .= "  , SUM(t.goals) as goals ";
			$query .= "  , ROUND(AVG(t.goals), 2) as avg_goals ";
			$query .= "FROM ( ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_home as team ";
			$query .= "    ,$cqa.$cq->bd_homegoals as goals  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= "  UNION  ";
			$query .= "  SELECT ";
			$query .= "    $cqa.$cq->bd_id as id ";
			$query .= "    ,$cqa.$cq->bd_away as team ";
			$query .= "    ,$cqa.$cq->bd_awaygoals as goals  ";
			$query .= "  FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales) . " ";
			$query .= ") as t ";
			$query .= "GROUP BY  ";
			$query .= "  t.team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_posession_query($at_home, $paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			if ($at_home == 1) {
				$field_team      = $cq->bd_home;
				$field_posession = $cq->bd_homeposession;
			} else {
				$field_team      = $cq->bd_away;
				$field_posession = $cq->bd_awayposession;
			}
			
			$query = "SELECT ";
			$query .= "  $cqa.$field_team AS team ";
			$query .= "  ,ROUND(AVG($cqa.$field_posession), 2) AS posession  ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales);
			$query .= "GROUP BY ";
			$query .= "  $cqa.$field_team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shots_query($at_home, $paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			if ($at_home == 1) {
				$field_team  = $cq->bd_home;
				$field_shots = $cq->bd_homeshots;
			} else {
				$field_team  = $cq->bd_away;
				$field_shots = $cq->bd_awayshots;
			}
			
			$query = "SELECT ";
			$query .= "  $cqa.$field_team AS team ";
			$query .= "  ,ROUND(AVG($cqa.$field_shots), 2) AS shots  ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales);
			$query .= "GROUP BY ";
			$query .= "  $cqa.$field_team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shotstarget_query($at_home, $paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			if ($at_home == 1) {
				$field_team        = $cq->bd_home;
				$field_shotstarget = $cq->bd_homeshotstarget;
			} else {
				$field_team        = $cq->bd_away;
				$field_shotstarget = $cq->bd_awayshotstarget;
			}
			
			$query = "SELECT ";
			$query .= "  $cqa.$field_team AS team ";
			$query .= "  ,ROUND(AVG($cqa.$field_shotstarget), 2) AS shotstarget  ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales);
			$query .= "GROUP BY ";
			$query .= "  $cqa.$field_team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_corners_query($at_home, $paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			if ($at_home == 1) {
				$field_team    = $cq->bd_home;
				$field_corners = $cq->bd_homecorners;
			} else {
				$field_team    = $cq->bd_away;
				$field_corners = $cq->bd_awaycorners;
			}
			
			$query = "SELECT ";
			$query .= "  $cqa.$field_team AS team ";
			$query .= "  ,SUM($cqa.$field_corners) AS corners  ";
			$query .= "  ,ROUND(AVG($cqa.$field_corners), 2) AS avg_corners  ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales);
			$query .= "GROUP BY ";
			$query .= "  $cqa.$field_team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_goals_query($at_home, $paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			if ($at_home == 1) {
				$field_team  = $cq->bd_home;
				$field_goals = $cq->bd_homegoals;
			} else {
				$field_team  = $cq->bd_away;
				$field_goals = $cq->bd_awaygoals;
			}
			
			$query = "SELECT ";
			$query .= "  $cqa.$field_team AS team ";
			$query .= "  ,SUM($cqa.$field_goals) AS goals  ";
			$query .= "  ,ROUND(AVG($cqa.$field_goals), 2) AS avg_goals  ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= self::get_rank_query_where($paises_adicionales);
			$query .= "GROUP BY ";
			$query .= "  $cqa.$field_team ";
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_query_where($paises_adicionales): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "WHERE ";
			$query .= "  $cqa.$cq->bd_status = :$cq->bd_status ";
			$query .= "  AND $cqa.$cq->bd_homecorners > -1 ";
			$query .= "  AND $cqa.$cq->bd_awaycorners > -1 ";
			$query .= "  AND $cqa.$cq->bd_homegoals > -1 ";
			$query .= "  AND $cqa.$cq->bd_awaygoals > -1 ";
			$query .= "  AND $cqa.$cq->bd_homeshots > -1 ";
			$query .= "  AND $cqa.$cq->bd_awayshots > -1 ";
			$query .= "  AND $cqa.$cq->bd_homeshotstarget > -1 ";
			$query .= "  AND $cqa.$cq->bd_awayshotstarget > -1 ";
			$query .= "  AND $cqa.$cq->bd_homeposession > 0 ";
			$query .= "  AND $cqa.$cq->bd_awayposession > 0 ";
			
			if (count($paises_adicionales) == 0) {
				$query .= "AND $cqa.$cq->bd_idpais = :$cq->bd_idpais ";
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$query .= "AND ($cqa.$cq->bd_idpais = :idpais1 ";
				
				for ($i = 0; $i < count($paises_adicionales); $i++) {
					$query .= "OR $cqa.$cq->bd_idpais = :idpais" . $n++ . " ";
				}
				
				$query .= ") ";
			}
			
			return $query;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_posession($paramref, $isvsall, $at_home, $conexion): array
	{
		try {
			$pais               = $paramref['pais'];
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$paises_adicionales = $paramref['paises_adicionales'];
			$idpais             = Pais::getByNombre($pais, $conexion);
			
			$cq = new self;
			
			if ($isvsall == 1) {
				$query = self::get_rank_posession_vsall_query($paises_adicionales);
			} else {
				$query = self::get_rank_posession_query($at_home, $paises_adicionales);
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", "COMPLETE");
			
			if (count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$statement->bindValue(":idpais1", ordena($idpais));
				
				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				$respuesta                   = array();
				$respuesta['rankhome']       = 0;
				$respuesta['rankaway']       = 0;
				$respuesta['rankhome_podio'] = '--';
				$respuesta['rankaway_podio'] = '--';
				
				return $respuesta;
				
			} else {
				$rankhome = 0;
				$rankaway = 0;
				$n        = 1;
				
				usort($resultados, function ($a, $b) {
					return $b['posession'] <=> $a['posession'];
				});
				
				foreach ($resultados as $resultado) {
					if ($home == $resultado['team']) {
						$rankhome = $n;
					}
					if ($away == $resultado['team']) {
						$rankaway = $n;
					}
					
					$n++;
				}
				
				$respuesta                   = array();
				$respuesta['rankhome']       = $rankhome;
				$respuesta['rankaway']       = $rankaway;
				$respuesta['rankhome_podio'] = $rankhome . '/' . count($resultados);
				$respuesta['rankaway_podio'] = $rankaway . '/' . count($resultados);
				
				return $respuesta;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shots($paramref, $isvsall, $at_home, $conexion): array
	{
		try {
			$pais               = $paramref['pais'];
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$paises_adicionales = $paramref['paises_adicionales'];
			$idpais             = Pais::getByNombre($pais, $conexion);
			
			$cq = new self;
			
			if ($isvsall == 1) {
				$query = self::get_rank_shots_vsall_query($paises_adicionales);
			} else {
				$query = self::get_rank_shots_query($at_home, $paises_adicionales);
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", "COMPLETE");
			
			if (count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$statement->bindValue(":idpais1", ordena($idpais));
				
				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				$respuesta                   = array();
				$respuesta['rankhome']       = 0;
				$respuesta['rankaway']       = 0;
				$respuesta['rankhome_podio'] = '--';
				$respuesta['rankaway_podio'] = '--';
				
				return $respuesta;
				
			} else {
				$rankhome = 0;
				$rankaway = 0;
				$n        = 1;
				
				usort($resultados, function ($a, $b) {
					return $b['shots'] <=> $a['shots'];
				});
				
				foreach ($resultados as $resultado) {
					if ($home == $resultado['team']) {
						$rankhome = $n;
					}
					if ($away == $resultado['team']) {
						$rankaway = $n;
					}
					
					$n++;
				}
				
				$respuesta                   = array();
				$respuesta['rankhome']       = $rankhome;
				$respuesta['rankaway']       = $rankaway;
				$respuesta['rankhome_podio'] = $rankhome . '/' . count($resultados);
				$respuesta['rankaway_podio'] = $rankaway . '/' . count($resultados);
				
				return $respuesta;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_shotstarget($paramref, $isvsall, $at_home, $conexion): array
	{
		try {
			$pais               = $paramref['pais'];
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$paises_adicionales = $paramref['paises_adicionales'];
			$idpais             = Pais::getByNombre($pais, $conexion);
			
			$cq = new self;
			
			if ($isvsall == 1) {
				$query = self::get_rank_shotstarget_vsall_query($paises_adicionales);
			} else {
				$query = self::get_rank_shotstarget_query($at_home, $paises_adicionales);
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", "COMPLETE");
			
			if (count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$statement->bindValue(":idpais1", ordena($idpais));
				
				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				$respuesta                   = array();
				$respuesta['rankhome']       = 0;
				$respuesta['rankaway']       = 0;
				$respuesta['rankhome_podio'] = '--';
				$respuesta['rankaway_podio'] = '--';
				
				return $respuesta;
				
			} else {
				$rankhome = 0;
				$rankaway = 0;
				$n        = 1;
				
				usort($resultados, function ($a, $b) {
					return $b['shotstarget'] <=> $a['shotstarget'];
				});
				
				foreach ($resultados as $resultado) {
					if ($home == $resultado['team']) {
						$rankhome = $n;
					}
					if ($away == $resultado['team']) {
						$rankaway = $n;
					}
					
					$n++;
				}
				
				$respuesta                   = array();
				$respuesta['rankhome']       = $rankhome;
				$respuesta['rankaway']       = $rankaway;
				$respuesta['rankhome_podio'] = $rankhome . '/' . count($resultados);
				$respuesta['rankaway_podio'] = $rankaway . '/' . count($resultados);
				
				return $respuesta;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_corners($paramref, $isvsall, $at_home, $conexion): array
	{
		try {
			$pais               = $paramref['pais'];
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$paises_adicionales = $paramref['paises_adicionales'];
			$idpais             = Pais::getByNombre($pais, $conexion);
			
			$cq = new self;
			
			if ($isvsall == 1) {
				$query = self::get_rank_corners_vsall_query($paises_adicionales);
			} else {
				$query = self::get_rank_corners_query($at_home, $paises_adicionales);
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", "COMPLETE");
			
			if (count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$statement->bindValue(":idpais1", ordena($idpais));
				
				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				$respuesta                   = array();
				$respuesta['rankhome']       = 0;
				$respuesta['rankaway']       = 0;
				$respuesta['rankhome_podio'] = '--';
				$respuesta['rankaway_podio'] = '--';
				$respuesta['rank_list']      = array();
				
				return $respuesta;
				
			} else {
				$rankhome = 0;
				$rankaway = 0;
				$n        = 1;
				
				usort($resultados, function ($a, $b) {
					return $b['avg_corners'] <=> $a['avg_corners'];
				});
				
				foreach ($resultados as $resultado) {
					if ($home == $resultado['team']) {
						$rankhome = $n;
					}
					if ($away == $resultado['team']) {
						$rankaway = $n;
					}
					
					$n++;
				}
				
				$respuesta                   = array();
				$respuesta['rankhome']       = $rankhome;
				$respuesta['rankaway']       = $rankaway;
				$respuesta['rankhome_podio'] = $rankhome . '/' . count($resultados);
				$respuesta['rankaway_podio'] = $rankaway . '/' . count($resultados);
				$respuesta['rank_list']      = $resultados;
				
				return $respuesta;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_rank_goals($paramref, $isvsall, $at_home, $conexion): array
	{
		try {
			$pais               = $paramref['pais'];
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$paises_adicionales = $paramref['paises_adicionales'];
			$idpais             = Pais::getByNombre($pais, $conexion);
			
			$cq = new self;
			
			if ($isvsall == 1) {
				$query = self::get_rank_goals_vsall_query($paises_adicionales);
			} else {
				$query = self::get_rank_goals_query($at_home, $paises_adicionales);
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", "COMPLETE");
			
			if (count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (count($paises_adicionales) > 0) {
				$n = 2;
				
				$statement->bindValue(":idpais1", ordena($idpais));
				
				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				$respuesta                   = array();
				$respuesta['rankhome']       = 0;
				$respuesta['rankaway']       = 0;
				$respuesta['rankhome_podio'] = '--';
				$respuesta['rankaway_podio'] = '--';
				$respuesta['rank_list']      = array();
				
			} else {
				$rankhome = 0;
				$rankaway = 0;
				$n        = 1;
				
				usort($resultados, function ($a, $b) {
					return $b['avg_goals'] <=> $a['avg_goals'];
				});
				
				foreach ($resultados as $resultado) {
					if ($home == $resultado['team']) {
						$rankhome = $n;
					}
					if ($away == $resultado['team']) {
						$rankaway = $n;
					}
					
					$n++;
				}
				
				$respuesta                   = array();
				$respuesta['rankhome']       = $rankhome;
				$respuesta['rankaway']       = $rankaway;
				$respuesta['rankhome_podio'] = $rankhome . '/' . count($resultados);
				$respuesta['rankaway_podio'] = $rankaway . '/' . count($resultados);
				$respuesta['rank_list']      = $resultados;
			}
			
			return $respuesta;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_seasons_byidpais($idpais, $conexion): string
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query     = "SELECT ";
			$query     .= "  $cqa.$cq->bd_season ";
			$query     .= "FROM $cq->bd_table $cq->bd_alias ";
			$query     .= "WHERE ";
			$query     .= "  $cqa.$cq->bd_status = :$cq->bd_status ";
			$query     .= "  AND $cqa.$cq->bd_idpais = :$cq->bd_idpais ";
			$query     .= "GROUP BY ";
			$query     .= "  $cqa.$cq->bd_season ";
			$query     .= "ORDER BY ";
			$query     .= "  $cqa.$cq->bd_season ";
			$statement = $conexion->prepare($query);
			
			//BEGIN bindvalue
			$statement->bindValue(":$cq->bd_status", 'COMPLETE');
			$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			//END bindvalue
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return '';
			} else {
				$seasons = '';
				
				foreach ($resultados as $resultado) {
					if (empty($seasons)) {
						$seasons = $resultado[$cq->bd_season];
					} else {
						$seasons .= ', ' . $resultado[$cq->bd_season];
					}
				}
				
				return $seasons;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getList($paramref, $conexion): array
	{
		try {
			$idpais             = (isset($paramref['idpais'])) ? $paramref['idpais'] : '';
			$home               = (isset($paramref['home'])) ? $paramref['home'] : '';
			$away               = (isset($paramref['away'])) ? $paramref['away'] : '';
			$season             = (isset($paramref['season'])) ? $paramref['season'] : '';
			$pais               = (isset($paramref['pais'])) ? $paramref['pais'] : '';
			$paises_adicionales = (isset($paramref['paises_adicionales'])) ? $paramref['paises_adicionales'] : array();
			$home_search        = (isset($paramref['home_search'])) ? $paramref['home_search'] : '';
			$away_search        = (isset($paramref['away_search'])) ? $paramref['away_search'] : '';
			$home_search_equal  = (isset($paramref['home_search_equal'])) ? $paramref['home_search_equal'] : '';
			$away_search_equal  = (isset($paramref['away_search_equal'])) ? $paramref['away_search_equal'] : '';
			$orderby_fecha      = (isset($paramref['orderby_fecha'])) ? $paramref['orderby_fecha'] : 0;
			$limit_12           = (isset($paramref['limit_12'])) ? $paramref['limit_12'] : 0;
			
			$cq      = new self;
			$cqa     = $cq->bd_alias;
			$cqpais  = new Pais;
			$cqapais = $cqpais->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "  ,$cqapais.$cqpais->bd_nombre nom_pais ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "INNER JOIN $cqpais->bd_table $cqapais ";
			$query .= "  ON ($cqapais.$cqpais->bd_id = $cqa.$cq->bd_idpais) ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_status = :$cq->bd_status ";
			$query .= "  AND $cqa.$cq->bd_homecorners > -1 ";
			$query .= "  AND $cqa.$cq->bd_awaycorners > -1 ";
			$query .= "  AND $cqa.$cq->bd_homegoals > -1 ";
			$query .= "  AND $cqa.$cq->bd_awaygoals > -1 ";
			$query .= "  AND $cqa.$cq->bd_homeshots > -1 ";
			$query .= "  AND $cqa.$cq->bd_awayshots > -1 ";
			$query .= "  AND $cqa.$cq->bd_homeshotstarget > -1 ";
			$query .= "  AND $cqa.$cq->bd_awayshotstarget > -1 ";
			/*$query .= "  AND $cqa.$cq->bd_homeposession > -1 ";
            $query .= "  AND $cqa.$cq->bd_awayposession > -1 ";*/
			
			#region region where
			if (!empty($home)) {
				$query .= "AND ( ";
				$query .= "$cqa.$cq->bd_home = :homeh ";
				$query .= "OR $cqa.$cq->bd_home = :awayh ";
				$query .= "OR $cqa.$cq->bd_away = :awaya ";
				$query .= "OR $cqa.$cq->bd_away = :homea ";
				$query .= ") ";
			}
			if (!empty($season)) {
				$query .= "AND $cqa.$cq->bd_season = :$cq->bd_season ";
			}
			if (!empty($idpais)) {
				$query .= "AND $cqa.$cq->bd_idpais = :$cq->bd_idpais ";
			}
			if (!empty($pais) && count($paises_adicionales) == 0) {
				$idpais = Pais::getByNombre($pais, $conexion);
				
				$query .= "AND $cqa.$cq->bd_idpais = :$cq->bd_idpais ";
			}
			if (!empty($pais) && count($paises_adicionales) > 0) {
				$idpais = Pais::getByNombre($pais, $conexion);
				$n      = 2;

				$query .= "AND ($cqa.$cq->bd_idpais = :idpais1 ";

				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$query .= "OR $cqa.$cq->bd_idpais = :idpais" . $n++ . " ";
				}

				$query .= ") ";
			}
			if (empty($pais) && count($paises_adicionales) > 0) {
				$n = 1;
				$query .= "AND (";

				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $index => $pais_adicional) {
					if ($index > 0) {
						$query .= " OR ";
					}
					$query .= "$cqa.$cq->bd_idpais = :idpais" . $n++;
				}

				$query .= ") ";
			}
			if (!empty($home_search)) {
				$query .= "AND $cqa.$cq->bd_home LIKE :$cq->bd_home ";
			}
			if (!empty($away_search)) {
				$query .= "AND $cqa.$cq->bd_away LIKE :$cq->bd_away ";
			}
			if (!empty($home_search_equal)) {
				$query .= "AND $cqa.$cq->bd_home = :$cq->bd_home ";
			}
			if (!empty($away_search_equal)) {
				$query .= "AND $cqa.$cq->bd_away = :$cq->bd_away ";
			}
			#endregion where
			
			if ($orderby_fecha == 1) {
				$query .= "ORDER BY ";
				$query .= "  $cq->bd_fecha DESC ";
			}
			
			if ($limit_12 == 1) {
				$query .= "LIMIT 12 ";
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_status", 'COMPLETE');
			
			#region region bind value
			if (!empty($home)) {
				$statement->bindValue(":homeh", $home);
				$statement->bindValue(":homea", $home);
				$statement->bindValue(":awayh", $away);
				$statement->bindValue(":awaya", $away);
			}
			if (!empty($season)) {
				$statement->bindValue(":$cq->bd_season", $season);
			}
			if (!empty($idpais) && count($paises_adicionales) == 0) {
				$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			}
			if (!empty($idpais) && count($paises_adicionales) > 0) {
				$n = 2;

				$statement->bindValue(":idpais1", ordena($idpais));

				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			if (empty($pais) && count($paises_adicionales) > 0) {
				$n = 1;

				/** @var PartidoTorneo[] $paises_adicionales */
				foreach ($paises_adicionales as $pais_adicional) {
					$statement->bindValue(":idpais" . $n++, ordena($pais_adicional->pais->id));
				}
			}
			if (!empty($home_search)) {
				$statement->bindValue(":$cq->bd_home", '%' . $home_search . '%');
			}
			if (!empty($away_search)) {
				$statement->bindValue(":$cq->bd_away", '%' . $away_search . '%');
			}
			if (!empty($home_search_equal)) {
				$statement->bindValue(":$cq->bd_home", $home_search_equal);
			}
			if (!empty($away_search_equal)) {
				$statement->bindValue(":$cq->bd_away", $away_search_equal);
			}
			#endregion bind value
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_byhome_n_away($paramref, $conexion): array
	{
		try {
			$home = $paramref['home'];
			$away = $paramref['away'];
			
			$cqpartidoinfo  = new self;
			$cqapartidoinfo = $cqpartidoinfo->bd_alias;
			$cqpais         = new Pais;
			$cqapais        = $cqpais->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqapais.$cqpais->bd_id $cqpais->bd_id ";
			$query .= "  ,$cqapais.$cqpais->bd_nombre torneo ";
			$query .= "FROM $cqpartidoinfo->bd_table $cqpartidoinfo->bd_alias ";
			$query .= "INNER JOIN $cqpais->bd_table $cqapais ";
			$query .= "  ON ($cqapais.$cqpais->bd_id = $cqapartidoinfo.$cqpartidoinfo->bd_idpais) ";
			$query .= "WHERE ";
			$query .= "  $cqapartidoinfo.$cqpartidoinfo->bd_status = :$cqpartidoinfo->bd_status ";
			$query .= "  AND ( ";
			$query .= "    $cqapartidoinfo.$cqpartidoinfo->bd_home = :$cqpartidoinfo->bd_home ";
			$query .= "    OR $cqapartidoinfo.$cqpartidoinfo->bd_away = :$cqpartidoinfo->bd_away ";
			$query .= "    OR $cqapartidoinfo.$cqpartidoinfo->bd_away = :$cqpartidoinfo->bd_home ";
			$query .= "    OR $cqapartidoinfo.$cqpartidoinfo->bd_home = :$cqpartidoinfo->bd_away ";
			$query .= "  ) ";
			$query .= "GROUP BY ";
			$query .= "  $cqapartidoinfo.$cqpartidoinfo->bd_idpais ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cqpartidoinfo->bd_status", 'COMPLETE');
			$statement->bindValue(":$cqpartidoinfo->bd_home", $home);
			$statement->bindValue(":$cqpartidoinfo->bd_away", $away);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$item = array();
					
					$item['idpais'] = desordena($resultado[$cqpais->bd_id]);
					$item['torneo'] = $resultado['torneo'];
					
					$listado[] = $item;
				}
				
				return $listado;
			}
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListCountByPais($pais, $conexion): int
	{
		try {
			$idpais = Pais::getByNombre($pais, $conexion);
			
			if (empty($idpais)) {
				return 0;
			}
			
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query     = "SELECT ";
			$query     .= "  $cqa.* ";
			$query     .= "FROM $cq->bd_table $cq->bd_alias ";
			$query     .= "WHERE ";
			$query     .= "  $cqa.$cq->bd_status = :$cq->bd_status ";
			$query     .= "  AND $cqa.$cq->bd_idpais = :$cq->bd_idpais ";
			$statement = $conexion->prepare($query);
			
			//BEGIN bindvalue
			$statement->bindValue(":$cq->bd_status", 'COMPLETE');
			$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			//END bindvalue
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return 0;
			} else {
				return 1;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListTeams($conexion): array
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = " SELECT ";
			$query .= "  $cqa.$cq->bd_home AS team ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_home ";
			$query .= "UNION ";
			$query .= "SELECT ";
			$query .= "  $cqa.$cq->bd_away AS team ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_away ";
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$partidoinfo       = new self;
					$partidoinfo->team = $resultado['team'];
					
					$listado[] = $partidoinfo;
				}
				
				usort($listado, function ($a, $b) {
					return $a->team <=> $b->team;
				});
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list_torneos_grouped($paramref, $conexion): array
	{
		try {
			$nom_pais = (isset($paramref['nom_pais'])) ? $paramref['nom_pais'] : '';
			
			$cq    = new self;
			$cqa   = $cq->bd_alias;
			$cq_p  = new Pais;
			$cq_pa = $cq_p->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cq_pa.nombre ";
			$query .= "  ,$cqa.season ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "INNER JOIN $cq_p->bd_table $cq_pa ON ($cq_pa.$cq_p->bd_id = $cqa.$cq->bd_idpais) ";
			
			if (!empty($nom_pais)) {
				$query .= "AND $cq_pa.$cq_p->bd_nombre LIKE :$cq_p->bd_nombre ";
			}
			
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_idpais ";
			$query .= "  ,$cqa.$cq->bd_season ";
			$query .= "ORDER BY ";
			$query .= "  $cq_pa.$cq_p->bd_nombre ";
			$query .= "  ,$cqa.$cq->bd_season ";
			
			$statement = $conexion->prepare($query);
			
			if (!empty($nom_pais)) {
				$statement->bindValue(":$cq_p->bd_nombre", '%' . $nom_pais . '%');
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$torneo           = array();
					$torneo['nombre'] = $resultado['nombre'];
					$torneo['season'] = $resultado['season'];
					
					$listado[] = $torneo;
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getCriterios($paramref): array
	{
		try {
			$home = $paramref['home'];
			$away = $paramref['away'];
			
			$n = 0;
			
			$criterios       = array();
			$criterios[$n++] = $home . ' (H) vs ALL';
			$criterios[$n++] = $home . ' (H) @H vs ALL';
			$criterios[$n++] = $home . ' (H) @A vs ALL';
			$criterios[$n++] = $away . ' (A) vs ALL';
			$criterios[$n++] = $away . ' (A) @H vs ALL';
			$criterios[$n++] = $away . ' (A) @A vs ALL';
			
			return $criterios;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_criterios_default(): array
	{
		try {
			$n = 0;
			
			$criterios       = array();
			$criterios[$n++] = ' (H) vs ALL';
			$criterios[$n++] = ' (H) @H vs ALL';
			$criterios[$n++] = ' (H) @A vs ALL';
			$criterios[$n++] = ' (A) vs ALL';
			$criterios[$n++] = ' (A) @H vs ALL';
			$criterios[$n++] = ' (A) @A vs ALL';
			
			return $criterios;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_criterios_hvsa(): array
	{
		try {
			$n = 0;
			
			$criterios       = array();
			$criterios[$n++] = 'Team vs ALL';
			$criterios[$n++] = '(H) @H / (A) @A vs ALL';
			$criterios[$n++] = '(H) @A / (A) @H vs ALL';
			
			return $criterios;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function add($conexion): void
	{
		try {
			$this->validateData($conexion);
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "  $cq->bd_idpais ";
			$query .= "  ,$cq->bd_fecha ";
			$query .= "  ,$cq->bd_status ";
			$query .= "  ,$cq->bd_home ";
			$query .= "  ,$cq->bd_away ";
			$query .= "  ,$cq->bd_homecorners ";
			$query .= "  ,$cq->bd_awaycorners ";
			$query .= "  ,$cq->bd_homegoals ";
			$query .= "  ,$cq->bd_awaygoals ";
			$query .= "  ,$cq->bd_season ";
			$query .= "  ,$cq->bd_homeshots ";
			$query .= "  ,$cq->bd_awayshots ";
			$query .= "  ,$cq->bd_homeshotstarget ";
			$query .= "  ,$cq->bd_awayshotstarget ";
			$query .= "  ,$cq->bd_homeposession ";
			$query .= "  ,$cq->bd_awayposession ";
			$query .= "  ,$cq->bd_fecha_upload ";
			$query .= ") VALUES (";
			$query .= "  :$cq->bd_idpais ";
			$query .= "  ,:$cq->bd_fecha ";
			$query .= "  ,:$cq->bd_status ";
			$query .= "  ,:$cq->bd_home ";
			$query .= "  ,:$cq->bd_away ";
			$query .= "  ,:$cq->bd_homecorners ";
			$query .= "  ,:$cq->bd_awaycorners ";
			$query .= "  ,:$cq->bd_homegoals ";
			$query .= "  ,:$cq->bd_awaygoals ";
			$query .= "  ,:$cq->bd_season ";
			$query .= "  ,:$cq->bd_homeshots ";
			$query .= "  ,:$cq->bd_awayshots ";
			$query .= "  ,:$cq->bd_homeshotstarget ";
			$query .= "  ,:$cq->bd_awayshotstarget ";
			$query .= "  ,:$cq->bd_homeposession ";
			$query .= "  ,:$cq->bd_awayposession ";
			$query .= "  ,:$cq->bd_fecha_upload ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_idpais", ordena($this->pais->id));
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_status", $this->status);
			$statement->bindValue(":$cq->bd_home", $this->home);
			$statement->bindValue(":$cq->bd_away", $this->away);
			$statement->bindValue(":$cq->bd_homecorners", $this->homecorners);
			$statement->bindValue(":$cq->bd_awaycorners", $this->awaycorners);
			$statement->bindValue(":$cq->bd_homegoals", $this->homegoals);
			$statement->bindValue(":$cq->bd_awaygoals", $this->awaygoals);
			$statement->bindValue(":$cq->bd_season", $this->season);
			$statement->bindValue(":$cq->bd_homeshots", $this->homeshots);
			$statement->bindValue(":$cq->bd_awayshots", $this->awayshots);
			$statement->bindValue(":$cq->bd_homeshotstarget", $this->homeshotstarget);
			$statement->bindValue(":$cq->bd_awayshotstarget", $this->awayshotstarget);
			$statement->bindValue(":$cq->bd_homeposession", $this->homeposession);
			$statement->bindValue(":$cq->bd_awayposession", $this->awayposession);
			$statement->bindValue(":$cq->bd_fecha_upload", $this->fecha_upload);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function add_pais_season($paramref, PDO $conexion): void
	{
		try {
			#region region parametros
			$nom_pais      = (isset($paramref['nom_pais'])) ? $paramref['nom_pais'] : "";
			$season        = (isset($paramref['season'])) ? $paramref['season'] : "";
			$perc_progress = 0;
			#endregion parametros
			
			$param             = array();
			$param['nom_pais'] = $nom_pais;
			$param['season']   = $season;
			$mod_pais_season   = PaisSeason::get($param, $conexion);
			
			if (empty($mod_pais_season->id)) {
				$mod_pais_season                = new PaisSeason();
				$mod_pais_season->nombre        = $nom_pais;
				$mod_pais_season->season        = $season;
				$mod_pais_season->perc_progress = $perc_progress;
				$mod_pais_season->add($conexion);
			} else {
				$mod_pais_season->perc_progress = $perc_progress;
				$mod_pais_season->modify($conexion);
			}
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function uploadCSV($paramref, $conexion): void
	{
		try {
			$idpais     = $paramref['idpais'];
			$archivocsv = $paramref['archivocsv'];
			$season     = $paramref['season'];
			
			$n         = 0;
			$cur_fecha = create_date();
			
			foreach ($archivocsv as $filacsv) {
				$filacsv   = trim($filacsv);
				$separador = ',';
				
				if ($n > 0) {
					$pattern         = '#(").*?(")#';                          //detectar campos en el excel que tenga comilla doble.
					$campos_cleaned  = preg_replace($pattern, "0", $filacsv);
					$campos          = explode($separador, $campos_cleaned);
					$campofecha      = explode(" - ", trim($campos[1]));
					$fecha           = convertToStandardDate($campofecha[0]);
					$status          = mb_strtoupper(trim($campos[2]));
					$home            = trim($campos[4]);
					$away            = trim($campos[5]);
					$home            = str_replace("'", "", $home);
					$away            = str_replace("'", "", $away);
					$homegoals       = trim($campos[12]);
					$awaygoals       = trim($campos[13]);
					$homecorners     = trim($campos[20]);
					$awaycorners     = trim($campos[21]);
					$homeshots       = trim($campos[30]);
					$awayshots       = trim($campos[31]);
					$homeshotstarget = trim($campos[32]);
					$awayshotstarget = trim($campos[33]);
					$homeposession   = trim($campos[38]);
					$awayposession   = trim($campos[39]);
					$omitir          = 0;
					
					if ($status == 'INCOMPLETE') {
						$omitir = 1;
					}
					
					if ($omitir == 0) {
						$newpartidoinfo                  = new self;
						$newpartidoinfo->fecha           = $fecha;
						$newpartidoinfo->status          = $status;
						$newpartidoinfo->home            = $home;
						$newpartidoinfo->away            = $away;
						$newpartidoinfo->homegoals       = $homegoals;
						$newpartidoinfo->awaygoals       = $awaygoals;
						$newpartidoinfo->homecorners     = $homecorners;
						$newpartidoinfo->awaycorners     = $awaycorners;
						$newpartidoinfo->pais            = new Pais;
						$newpartidoinfo->pais->id        = $idpais;
						$newpartidoinfo->season          = $season;
						$newpartidoinfo->homeshots       = $homeshots;
						$newpartidoinfo->awayshots       = $awayshots;
						$newpartidoinfo->homeshotstarget = $homeshotstarget;
						$newpartidoinfo->awayshotstarget = $awayshotstarget;
						$newpartidoinfo->homeposession   = $homeposession;
						$newpartidoinfo->awayposession   = $awayposession;
						$newpartidoinfo->fecha_upload    = $cur_fecha;
						$newpartidoinfo->add($conexion);
					}
				}
				
				$n++;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modify($conexion): void
	{
		try {
			$this->validateData($conexion);
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_fecha = :$cq->bd_fecha ";
			$query .= "  ,$cq->bd_status = :$cq->bd_status ";
			$query .= "  ,$cq->bd_home = :$cq->bd_home ";
			$query .= "  ,$cq->bd_away = :$cq->bd_away ";
			$query .= "  ,$cq->bd_homecorners = :$cq->bd_homecorners ";
			$query .= "  ,$cq->bd_awaycorners = :$cq->bd_awaycorners ";
			$query .= "  ,$cq->bd_awaycorners = :$cq->bd_awaycorners ";
			$query .= "  ,$cq->bd_idpais = :$cq->bd_idpais ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_status", $this->status);
			$statement->bindValue(":$cq->bd_home", $this->home);
			$statement->bindValue(":$cq->bd_away", $this->away);
			$statement->bindValue(":$cq->bd_homecorners", $this->homecorners);
			$statement->bindValue(":$cq->bd_awaycorners", $this->awaycorners);
			$statement->bindValue(":$cq->bd_idpais", ordena($this->pais->id));
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modify_corregir_team($paramref, $conexion): void
	{
		try {
			self::validate_data_corregirteam($paramref);
			
			$idpais = Pais::getByNombre($paramref['pais'], $conexion);
			
			if (empty($idpais)) {
				throw new Exception('No existe el torneo ' . $paramref['pais']);
			}
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_home = :team_corregido ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_home = :team_acorregir ";
			$query .= "  AND $cq->bd_idpais = :$cq->bd_idpais ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":team_corregido", mb_strtoupper(trim($paramref['team_corregido'])));
			$statement->bindValue(":team_acorregir", mb_strtoupper(trim($paramref['team_acorregir'])));
			$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			$statement->execute();
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_away = :team_corregido ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_away = :team_acorregir ";
			$query .= "  AND $cq->bd_idpais = :$cq->bd_idpais ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":team_corregido", mb_strtoupper($paramref['team_corregido']));
			$statement->bindValue(":team_acorregir", mb_strtoupper($paramref['team_acorregir']));
			$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function deleteSelected($paramref, $conexion): void
	{
		try {
			$idpais = $paramref['idpais'];
			$season = $paramref['season'];
			
			self::validateDataDeleteSelected($idpais, $season);
			
			$cq = new self;
			
			$query = " DELETE FROM $cq->bd_table WHERE";
			$query .= "  $cq->bd_idpais = :$cq->bd_idpais ";
			$query .= "  AND $cq->bd_season = :$cq->bd_season ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_season", $season);
			$statement->bindValue(":$cq->bd_idpais", ordena($idpais));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularNumeroPartidos($paramref): array
	{
		try {
			$home         = $paramref['home'];
			$away         = $paramref['away'];
			$partidosinfo = $paramref['partidosinfo'];
			
			$numeropartidos = self::constructArrayCriterios();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[0]++;
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[1]++;
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[2]++;
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[3]++;
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[4]++;
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos[5]++;
				}
			}
			
			return $numeropartidos;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularPromedioCorners($paramref): array
	{
		try {
			$home                 = $paramref['home'];
			$away                 = $paramref['away'];
			$partidosinfo         = $paramref['partidosinfo'];
			$numeropartidos       = $paramref['numeropartidos'];
			$totalcorners_masde   = $paramref['totalcorners_masde'];
			$totalcorners_menosde = $paramref['totalcorners_menosde'];
			$cornershome_masde    = $paramref['cornershome_masde'];
			$cornershome_menosde  = $paramref['cornershome_menosde'];
			$cornersaway_masde    = $paramref['cornersaway_masde'];
			$cornersaway_menosde  = $paramref['cornersaway_menosde'];
			
			$sumatotalcorners = self::constructArrayCriterios();
			$sumacornershome  = self::constructArrayCriterios();
			$sumacornersaway  = self::constructArrayCriterios();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar corners por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[0] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornershome[0]  += self::sumarCornersAEquipo($partidoinfo, $home);
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[1] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornershome[1]  += self::sumarCornersAEquipo($partidoinfo, $home);
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[2] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornershome[2]  += self::sumarCornersAEquipo($partidoinfo, $home);
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[3] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornersaway[3]  += self::sumarCornersAEquipo($partidoinfo, $away);
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[4] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornersaway[4]  += self::sumarCornersAEquipo($partidoinfo, $away);
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotalcorners[5] += $partidoinfo->homecorners + $partidoinfo->awaycorners;
					$sumacornersaway[5]  += self::sumarCornersAEquipo($partidoinfo, $away);
				}
			}
			
			//calcular promedios
			$promediototalcorners          = array();
			$promediocornershome           = array();
			$promediocornersaway           = array();
			$diff_masde                    = array();
			$diff_menosde                  = array();
			$suma_diff                     = array();
			$suma_diff['total']['masde']   = 0;
			$suma_diff['total']['menosde'] = 0;
			$suma_diff['home']['masde']    = 0;
			$suma_diff['home']['menosde']  = 0;
			$suma_diff['away']['masde']    = 0;
			$suma_diff['away']['menosde']  = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$promediototalcorners[$i] = round($sumatotalcorners[$i] / $numeropartidos[$i], 2);
					$promediocornershome[$i]  = round($sumacornershome[$i] / $numeropartidos[$i], 2);
					$promediocornersaway[$i]  = round($sumacornersaway[$i] / $numeropartidos[$i], 2);
					
					$diff_masde[$i]['total']   = round($promediototalcorners[$i] - $totalcorners_masde, 2);
					$diff_menosde[$i]['total'] = round($totalcorners_menosde - $promediototalcorners[$i], 2);
					$diff_masde[$i]['home']    = round($promediocornershome[$i] - $cornershome_masde, 2);
					$diff_menosde[$i]['home']  = round($cornershome_menosde - $promediocornershome[$i], 2);
					$diff_masde[$i]['away']    = round($promediocornersaway[$i] - $cornersaway_masde, 2);
					$diff_menosde[$i]['away']  = round($cornersaway_menosde - $promediocornersaway[$i], 2);
				} else {
					$promediototalcorners[$i]  = 0;
					$promediocornershome[$i]   = 0;
					$promediocornersaway[$i]   = 0;
					$diff_masde[$i]['total']   = 0;
					$diff_menosde[$i]['total'] = 0;
					$diff_masde[$i]['home']    = 0;
					$diff_menosde[$i]['home']  = 0;
					$diff_masde[$i]['away']    = 0;
					$diff_menosde[$i]['away']  = 0;
				}
				
				$suma_diff['total']['masde']   += $diff_masde[$i]['total'];
				$suma_diff['total']['menosde'] += $diff_menosde[$i]['total'];
				
				if ($promediocornershome[$i] > 0) {
					$suma_diff['home']['masde']   += $diff_masde[$i]['home'];
					$suma_diff['home']['menosde'] += $diff_menosde[$i]['home'];
				}
				if ($promediocornersaway[$i] > 0) {
					$suma_diff['away']['masde']   += $diff_masde[$i]['away'];
					$suma_diff['away']['menosde'] += $diff_menosde[$i]['away'];
				}
				
				$diff_masde[$i]['totalbgcolor']   = ($diff_masde[$i]['total'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['totalbgcolor'] = ($diff_menosde[$i]['total'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_masde[$i]['homebgcolor']    = ($diff_masde[$i]['home'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['homebgcolor']  = ($diff_menosde[$i]['home'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_masde[$i]['awaybgcolor']    = ($diff_masde[$i]['away'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['awaybgcolor']  = ($diff_menosde[$i]['away'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
			}
			
			$resultado                 = array();
			$resultado['total']        = $promediototalcorners;
			$resultado['home']         = $promediocornershome;
			$resultado['away']         = $promediocornersaway;
			$resultado['diff_masde']   = $diff_masde;
			$resultado['diff_menosde'] = $diff_menosde;
			$resultado['sumadiff']     = $suma_diff;
			
			return $resultado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_promedio_goles($paramref): array
	{
		try {
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$partidosinfo       = $paramref['partidosinfo'];
			$numeropartidos     = $paramref['numeropartidos'];
			$totalgoles_masde   = $paramref['totalgoles_masde'];
			$totalgoles_menosde = $paramref['totalgoles_menosde'];
			$goleshome_masde    = $paramref['goleshome_masde'];
			$goleshome_menosde  = $paramref['goleshome_menosde'];
			$golesaway_masde    = $paramref['golesaway_masde'];
			$golesaway_menosde  = $paramref['golesaway_menosde'];
			
			$sumatotal = self::constructArrayCriterios();
			$sumahome  = self::constructArrayCriterios();
			$sumaaway  = self::constructArrayCriterios();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar corners por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[0] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumahome[0]  += self::sumar_goles_aequipo($partidoinfo, $home);
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[1] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumahome[1]  += self::sumar_goles_aequipo($partidoinfo, $home);
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[2] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumahome[2]  += self::sumar_goles_aequipo($partidoinfo, $home);
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[3] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumaaway[3]  += self::sumar_goles_aequipo($partidoinfo, $away);
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[4] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumaaway[4]  += self::sumar_goles_aequipo($partidoinfo, $away);
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$sumatotal[5] += $partidoinfo->homegoals + $partidoinfo->awaygoals;
					$sumaaway[5]  += self::sumar_goles_aequipo($partidoinfo, $away);
				}
			}
			
			//calcular promedios
			$promediototal                 = array();
			$promediohome                  = array();
			$promedioaway                  = array();
			$diff_masde                    = array();
			$diff_menosde                  = array();
			$suma_diff                     = array();
			$suma_diff['total']['masde']   = 0;
			$suma_diff['total']['menosde'] = 0;
			$suma_diff['home']['masde']    = 0;
			$suma_diff['home']['menosde']  = 0;
			$suma_diff['away']['masde']    = 0;
			$suma_diff['away']['menosde']  = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$promediototal[$i] = round($sumatotal[$i] / $numeropartidos[$i], 2);
					$promediohome[$i]  = round($sumahome[$i] / $numeropartidos[$i], 2);
					$promedioaway[$i]  = round($sumaaway[$i] / $numeropartidos[$i], 2);
					
					$diff_masde[$i]['total']   = round($promediototal[$i] - $totalgoles_masde, 2);
					$diff_menosde[$i]['total'] = round($totalgoles_menosde - $promediototal[$i], 2);
					$diff_masde[$i]['home']    = round($promediohome[$i] - $goleshome_masde, 2);
					$diff_menosde[$i]['home']  = round($goleshome_menosde - $promediohome[$i], 2);
					$diff_masde[$i]['away']    = round($promedioaway[$i] - $golesaway_masde, 2);
					$diff_menosde[$i]['away']  = round($golesaway_menosde - $promedioaway[$i], 2);
				} else {
					$promediototal[$i]         = 0;
					$promediohome[$i]          = 0;
					$promedioaway[$i]          = 0;
					$diff_masde[$i]['total']   = 0;
					$diff_menosde[$i]['total'] = 0;
					$diff_masde[$i]['home']    = 0;
					$diff_menosde[$i]['home']  = 0;
					$diff_masde[$i]['away']    = 0;
					$diff_menosde[$i]['away']  = 0;
				}
				
				$suma_diff['total']['masde']   += $diff_masde[$i]['total'];
				$suma_diff['total']['menosde'] += $diff_menosde[$i]['total'];
				
				if ($promediohome[$i] > 0) {
					$suma_diff['home']['masde']   += $diff_masde[$i]['home'];
					$suma_diff['home']['menosde'] += $diff_menosde[$i]['home'];
				}
				if ($promedioaway[$i] > 0) {
					$suma_diff['away']['masde']   += $diff_masde[$i]['away'];
					$suma_diff['away']['menosde'] += $diff_menosde[$i]['away'];
				}
				
				$diff_masde[$i]['totalbgcolor']   = ($diff_masde[$i]['total'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['totalbgcolor'] = ($diff_menosde[$i]['total'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_masde[$i]['homebgcolor']    = ($diff_masde[$i]['home'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['homebgcolor']  = ($diff_menosde[$i]['home'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_masde[$i]['awaybgcolor']    = ($diff_masde[$i]['away'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
				$diff_menosde[$i]['awaybgcolor']  = ($diff_menosde[$i]['away'] > 0) ? TEXT_SUCCESS : TEXT_DANGER;
			}
			
			$resultado                 = array();
			$resultado['total']        = $promediototal;
			$resultado['home']         = $promediohome;
			$resultado['away']         = $promedioaway;
			$resultado['diff_masde']   = $diff_masde;
			$resultado['diff_menosde'] = $diff_menosde;
			$resultado['sumadiff']     = $suma_diff;
			
			return $resultado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function sumarCornersAEquipo($partidoinfo, $equipo): int
	{
		try {
			$corners_a_sumar = 0;
			
			if ($partidoinfo->home == $equipo) {
				$corners_a_sumar = $partidoinfo->homecorners;
			}
			if ($partidoinfo->away == $equipo) {
				$corners_a_sumar = $partidoinfo->awaycorners;
			}
			
			return $corners_a_sumar;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function sumar_goles_aequipo($partidoinfo, $equipo): int
	{
		try {
			/** @var self $partidoinfo */
			
			$goles_a_sumar = 0;
			
			if ($partidoinfo->home == $equipo) {
				$goles_a_sumar = $partidoinfo->homegoals;
			}
			if ($partidoinfo->away == $equipo) {
				$goles_a_sumar = $partidoinfo->awaygoals;
			}
			
			return $goles_a_sumar;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularProbabilidadTotalCorners($paramref): array
	{
		try {
			$home                 = $paramref['home'];
			$away                 = $paramref['away'];
			$partidosinfo         = $paramref['partidosinfo'];
			$numeropartidos       = $paramref['numeropartidos'];
			$totalcorners_masde   = $paramref['totalcorners_masde'];
			$totalcorners_menosde = $paramref['totalcorners_menosde'];
			
			$numeropartidos_cumple = self::constructArrayRangos();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar corners por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[0]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[0]['menosde']++;
					}
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[1]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[1]['menosde']++;
					}
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[2]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[2]['menosde']++;
					}
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[3]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[3]['menosde']++;
					}
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[4]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[4]['menosde']++;
					}
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					//verificar si el partido cumple con el criterio y sumar +1 si es asi.
					if (self::isTotalCornersMasDe($partidoinfo, $totalcorners_masde) == 1) {
						$numeropartidos_cumple[5]['masde']++;
					}
					if (self::isTotalCornersMenosDe($partidoinfo, $totalcorners_menosde) == 1) {
						$numeropartidos_cumple[5]['menosde']++;
					}
				}
			}
			
			//calcular probabilidades de cada criterio
			$probabilidades = self::constructArrayRangos();
			
			$sumaprobabilidades                      = array();
			$sumaprobabilidades['masde']             = 0;
			$sumaprobabilidades['menosde']           = 0;
			$sumaprobabilidades['masde_important']   = 0;
			$sumaprobabilidades['menosde_important'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($numeropartidos_cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($numeropartidos_cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					
					$sumaprobabilidades['masde']   += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde'] += $probabilidades[$i]['menosde'];
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
				}
				
				//Sumar solo las probabilidades de home @h y away @a
				if ($i == 1 || $i == 5) {
					if ($probabilidades[$i]['masde'] > 0) {
						$sumaprobabilidades['masde_important'] += $probabilidades[$i]['masde'];
					}
					if ($probabilidades[$i]['menosde'] > 0) {
						$sumaprobabilidades['menosde_important'] += $probabilidades[$i]['menosde'];
					}
				}
			}
			
			$probabilidades['promedio']['masde']               = round($sumaprobabilidades['masde'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['masdebg']             = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']             = round($sumaprobabilidades['menosde'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['menosdebg']           = self::definirBgColor($probabilidades['promedio']['menosde']);
			$probabilidades['promedio']['masde_important']     = round($sumaprobabilidades['masde_important'] / 2);
			$probabilidades['promedio']['masdebg_important']   = self::definirBgColor($probabilidades['promedio']['masde_important']);
			$probabilidades['promedio']['menosde_important']   = round($sumaprobabilidades['menosde_important'] / 2);
			$probabilidades['promedio']['menosdebg_important'] = self::definirBgColor($probabilidades['promedio']['menosde_important']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularProbabilidadCornersHome($paramref): array
	{
		try {
			$home                  = $paramref['home'];
			$away                  = $paramref['away'];
			$partidosinfo          = $paramref['partidosinfo'];
			$numeropartidos        = $paramref['numeropartidos'];
			$cornershome_masde     = $paramref['cornershome_masde'];
			$cornershome_menosde   = $paramref['cornershome_menosde'];
			$numeropartidos_cumple = self::constructArrayRangos();
			$conceded_corners      = self::constructArrayRangosEmpty();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar corners por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[0]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_masde, 'masde');
					$numeropartidos_cumple[0]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_menosde, 'menosde');
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[1]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_masde, 'masde');
					$numeropartidos_cumple[1]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_menosde, 'menosde');
					$conceded_corners[2]                 += $partidoinfo->awaycorners;
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[2]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_masde, 'masde');
					$numeropartidos_cumple[2]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $home, $cornershome_menosde, 'menosde');
					$conceded_corners[1]                 += $partidoinfo->homecorners;
				}
			}
			
			//calcular probabilidades de cada criterio
			$prom_conceded_corners         = self::constructArrayRangosEmpty();
			$probabilidades                = self::constructArrayRangos();
			$sumaprobabilidades            = array();
			$sumaprobabilidades['masde']   = 0;
			$sumaprobabilidades['menosde'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($numeropartidos_cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($numeropartidos_cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					$sumaprobabilidades['masde']     += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde']   += $probabilidades[$i]['menosde'];
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
					$prom_conceded_corners[$i]       = 0;
				}
			}
			
			//calcular corners concedidos promedios de away vs all
			if ($numeropartidos[2] > 0) {
				$prom_conceded_corners[1] = round(($conceded_corners[1] / $numeropartidos[2]), 2);
			} else {
				$prom_conceded_corners[1] = 0;
			}
			if ($numeropartidos[1] > 0) {
				$prom_conceded_corners[2] = round(($conceded_corners[2] / $numeropartidos[1]), 2);
			} else {
				$prom_conceded_corners[2] = 0;
			}
			
			$prom_conceded_corners[0] = round((($prom_conceded_corners[1] + $prom_conceded_corners[2]) / 2), 2);
			
			//calcular promedio de probabilidades de todo
			$probabilidades['promedio']['masde']     = round($sumaprobabilidades['masde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['masdebg']   = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']   = round($sumaprobabilidades['menosde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['menosdebg'] = self::definirBgColor($probabilidades['promedio']['menosde']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			$resultados['conceded']       = $prom_conceded_corners;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularProbabilidadCornersAway($paramref): array
	{
		try {
			$home                  = $paramref['home'];
			$away                  = $paramref['away'];
			$partidosinfo          = $paramref['partidosinfo'];
			$numeropartidos        = $paramref['numeropartidos'];
			$cornersaway_masde     = $paramref['cornersaway_masde'];
			$cornersaway_menosde   = $paramref['cornersaway_menosde'];
			$numeropartidos_cumple = self::constructArrayRangos();
			$conceded_corners      = self::constructArrayRangosEmpty();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar corners por cada criterio para luego sacar el promedio
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[3]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_masde, 'masde');
					$numeropartidos_cumple[3]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_menosde, 'menosde');
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[4]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_masde, 'masde');
					$numeropartidos_cumple[4]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_menosde, 'menosde');
					$conceded_corners[5]                 += $partidoinfo->awaycorners;
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[5]['masde']   += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_masde, 'masde');
					$numeropartidos_cumple[5]['menosde'] += self::isCornersEquipoMasMenosDe($partidoinfo, $away, $cornersaway_menosde, 'menosde');
					$conceded_corners[4]                 += $partidoinfo->homecorners;
				}
			}
			
			//calcular probabilidades de cada criterio
			$prom_conceded_corners         = self::constructArrayRangosEmpty();
			$probabilidades                = self::constructArrayRangos();
			$sumaprobabilidades            = array();
			$sumaprobabilidades['masde']   = 0;
			$sumaprobabilidades['menosde'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($numeropartidos_cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($numeropartidos_cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					$sumaprobabilidades['masde']     += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde']   += $probabilidades[$i]['menosde'];
					
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
					$prom_conceded_corners[$i]       = 0;
				}
			}
			
			//calcular corners concedidos promedios de away vs all
			if ($numeropartidos[5] > 0) {
				$prom_conceded_corners[4] = round($conceded_corners[4] / $numeropartidos[5], 2);
			} else {
				$prom_conceded_corners[4] = 0;
			}
			if ($numeropartidos[4] > 0) {
				$prom_conceded_corners[5] = round($conceded_corners[5] / $numeropartidos[4], 2);
			} else {
				$prom_conceded_corners[5] = 0;
			}
			
			$prom_conceded_corners[3] = round(($prom_conceded_corners[4] + $prom_conceded_corners[5]) / 2, 2);
			
			//calcular promedio de probabilidades de todo
			$probabilidades['promedio']['masde']     = round($sumaprobabilidades['masde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['masdebg']   = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']   = round($sumaprobabilidades['menosde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['menosdebg'] = self::definirBgColor($probabilidades['promedio']['menosde']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			$resultados['conceded']       = $prom_conceded_corners;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularProbabilidadTotalGoles($paramref): array
	{
		try {
			$home               = $paramref['home'];
			$away               = $paramref['away'];
			$partidosinfo       = $paramref['partidosinfo'];
			$numeropartidos     = $paramref['numeropartidos'];
			$totalgoles_masde   = $paramref['totalgoles_masde'];
			$totalgoles_menosde = $paramref['totalgoles_menosde'];
			
			$numeropartidos_cumple = self::constructArrayRangos();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar goles por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[0]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[0]['menosde']++;
					}
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[1]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[1]['menosde']++;
					}
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[2]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[2]['menosde']++;
					}
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[3]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[3]['menosde']++;
					}
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[4]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[4]['menosde']++;
					}
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isTotalGolesMasDe($partidoinfo, $totalgoles_masde) == 1) {
						$numeropartidos_cumple[5]['masde']++;
					}
					if (self::isTotalGolesMenosDe($partidoinfo, $totalgoles_menosde) == 1) {
						$numeropartidos_cumple[5]['menosde']++;
					}
				}
			}
			
			//calcular probabilidades de cada criterio
			$probabilidades = self::constructArrayRangos();
			
			$sumaprobabilidades                      = array();
			$sumaprobabilidades['masde']             = 0;
			$sumaprobabilidades['menosde']           = 0;
			$sumaprobabilidades['masde_important']   = 0;
			$sumaprobabilidades['menosde_important'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($numeropartidos_cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($numeropartidos_cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					
					$sumaprobabilidades['masde']   += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde'] += $probabilidades[$i]['menosde'];
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
				}
				
				//Sumar solo las probabilidades de home @h y away @a
				if ($i == 1 || $i == 5) {
					if ($probabilidades[$i]['masde'] > 0) {
						$sumaprobabilidades['masde_important'] += $probabilidades[$i]['masde'];
					}
					if ($probabilidades[$i]['menosde'] > 0) {
						$sumaprobabilidades['menosde_important'] += $probabilidades[$i]['menosde'];
					}
				}
			}
			
			$probabilidades['promedio']['masde']               = round($sumaprobabilidades['masde'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['masdebg']             = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']             = round($sumaprobabilidades['menosde'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['menosdebg']           = self::definirBgColor($probabilidades['promedio']['menosde']);
			$probabilidades['promedio']['masde_important']     = round($sumaprobabilidades['masde_important'] / 2);
			$probabilidades['promedio']['masdebg_important']   = self::definirBgColor($probabilidades['promedio']['masde_important']);
			$probabilidades['promedio']['menosde_important']   = round($sumaprobabilidades['menosde_important'] / 2);
			$probabilidades['promedio']['menosdebg_important'] = self::definirBgColor($probabilidades['promedio']['menosde_important']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_probabilidades_hvsa($paramref): array
	{
		try {
			$home           = $paramref['home'];
			$away           = $paramref['away'];
			$partidosinfo   = $paramref['partidosinfo'];
			$numeropartidos = $paramref['numeropartidos'];
			
			$numeropartidos_cumple = self::constructArrayRangos();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar goles por cada criterio para luego sacar el promedio
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[0]['home_wins'] += self::is_home_wins($partidoinfo, $home);
					$numeropartidos_cumple[0]['away_wins'] += 0;
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[1]['home_wins'] += self::is_home_wins_athome($partidoinfo, $home);
					$numeropartidos_cumple[1]['away_wins'] += 0;
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[2]['home_wins'] += self::is_home_wins_ataway($partidoinfo, $home);
					$numeropartidos_cumple[2]['away_wins'] += 0;
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[3]['home_wins'] += 0;
					$numeropartidos_cumple[3]['away_wins'] += self::is_away_wins($partidoinfo, $away);
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[4]['home_wins'] += 0;
					$numeropartidos_cumple[4]['away_wins'] += self::is_away_wins_athome($partidoinfo, $away);
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$numeropartidos_cumple[5]['home_wins'] += 0;
					$numeropartidos_cumple[5]['away_wins'] += self::is_away_wins_ataway($partidoinfo, $away);
				}
			}
			
			//calcular probabilidades de cada criterio
			$probabilidades = self::constructArrayRangos();
			
			$sumaprobabilidades              = array();
			$sumaprobabilidades['home_wins'] = 0;
			$sumaprobabilidades['away_wins'] = 0;
			
			for ($i = 0; $i < 3; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['home_wins']   = round(($numeropartidos_cumple[$i]['home_wins'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['home_winsbg'] = self::definirBgColor($probabilidades[$i]['home_wins']);
					
					$sumaprobabilidades['home_wins'] += $probabilidades[$i]['home_wins'];
				} else {
					$probabilidades[$i]['home_wins']   = 0;
					$probabilidades[$i]['home_winsbg'] = 0;
				}
			}
			
			for ($i = 3; $i < 6; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['away_wins']   = round(($numeropartidos_cumple[$i]['away_wins'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['away_winsbg'] = self::definirBgColor($probabilidades[$i]['away_wins']);
					
					$sumaprobabilidades['away_wins'] += $probabilidades[$i]['away_wins'];
				} else {
					$probabilidades[$i]['away_wins']   = 0;
					$probabilidades[$i]['away_winsbg'] = 0;
				}
			}
			
			$probabilidades['promedio']['home_wins']   = round($sumaprobabilidades['home_wins'] / 3);
			$probabilidades['promedio']['home_winsbg'] = self::definirBgColor($probabilidades['promedio']['home_wins']);
			$probabilidades['promedio']['away_wins']   = round($sumaprobabilidades['away_wins'] / 3);
			$probabilidades['promedio']['away_winsbg'] = self::definirBgColor($probabilidades['promedio']['away_wins']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_probabilidades_goleshome($paramref): array
	{
		try {
			$home              = $paramref['home'];
			$away              = $paramref['away'];
			$partidosinfo      = $paramref['partidosinfo'];
			$numeropartidos    = $paramref['numeropartidos'];
			$goleshome_masde   = $paramref['goleshome_masde'];
			$goleshome_menosde = $paramref['goleshome_menosde'];
			$cumple            = self::constructArrayRangos();
			$conceded          = self::constructArrayRangosEmpty();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar numero de partidos que cumplan con cada criterio.
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					$cumple[0]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_masde, 'masde');
					$cumple[0]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_menosde, 'menosde');
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$cumple[1]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_masde, 'masde');
					$cumple[1]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_menosde, 'menosde');
					$conceded[2]          += $partidoinfo->awaygoals;
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$cumple[2]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_masde, 'masde');
					$cumple[2]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $home, $goleshome_menosde, 'menosde');
					$conceded[1]          += $partidoinfo->homegoals;
				}
			}
			
			//calcular probabilidades de cada criterio
			$avg_conceded                  = self::constructArrayRangosEmpty();
			$probabilidades                = self::constructArrayRangos();
			$sumaprobabilidades            = array();
			$sumaprobabilidades['masde']   = 0;
			$sumaprobabilidades['menosde'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					$sumaprobabilidades['masde']     += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde']   += $probabilidades[$i]['menosde'];
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
					$avg_conceded[$i]                = 0;
				}
			}
			
			//calcular goles concedidos promedios de away vs all
			if ($numeropartidos[2] > 0) {
				$avg_conceded[1] = round(($conceded[1] / $numeropartidos[2]), 2);
			} else {
				$avg_conceded[1] = 0;
			}
			if ($numeropartidos[1] > 0) {
				$avg_conceded[2] = round(($conceded[2] / $numeropartidos[1]), 2);
			} else {
				$avg_conceded[2] = 0;
			}
			
			$avg_conceded[0] = round((($avg_conceded[1] + $avg_conceded[2]) / 2), 2);
			
			//calcular promedio de probabilidades de todo
			$probabilidades['promedio']['masde']     = round($sumaprobabilidades['masde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['masdebg']   = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']   = round($sumaprobabilidades['menosde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['menosdebg'] = self::definirBgColor($probabilidades['promedio']['menosde']);
			
			$resultados                   = array();
			$resultados['cumple']         = $cumple;
			$resultados['probabilidades'] = $probabilidades;
			$resultados['conceded']       = $avg_conceded;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_probabilidades_golesaway($paramref): array
	{
		try {
			$home              = $paramref['home'];
			$away              = $paramref['away'];
			$partidosinfo      = $paramref['partidosinfo'];
			$numeropartidos    = $paramref['numeropartidos'];
			$golesaway_masde   = $paramref['golesaway_masde'];
			$golesaway_menosde = $paramref['golesaway_menosde'];
			$cumple            = self::constructArrayRangos();
			$conceded          = self::constructArrayRangosEmpty();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				//sumar goles por cada criterio para luego sacar el promedio
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					$cumple[3]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_masde, 'masde');
					$cumple[3]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_menosde, 'menosde');
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					$cumple[4]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_masde, 'masde');
					$cumple[4]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_menosde, 'menosde');
					$conceded[5]          += $partidoinfo->awaygoals;
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					$cumple[5]['masde']   += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_masde, 'masde');
					$cumple[5]['menosde'] += self::isgoles_equipo_masmenosde($partidoinfo, $away, $golesaway_menosde, 'menosde');
					$conceded[4]          += $partidoinfo->homegoals;
				}
			}
			
			//calcular probabilidades de cada criterio
			$avg_conceded                  = self::constructArrayRangosEmpty();
			$probabilidades                = self::constructArrayRangos();
			$sumaprobabilidades            = array();
			$sumaprobabilidades['masde']   = 0;
			$sumaprobabilidades['menosde'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['masde']     = round(($cumple[$i]['masde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['menosde']   = round(($cumple[$i]['menosde'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['masdebg']   = self::definirBgColor($probabilidades[$i]['masde']);
					$probabilidades[$i]['menosdebg'] = self::definirBgColor($probabilidades[$i]['menosde']);
					$sumaprobabilidades['masde']     += $probabilidades[$i]['masde'];
					$sumaprobabilidades['menosde']   += $probabilidades[$i]['menosde'];
					
				} else {
					$probabilidades[$i]['masde']     = 0;
					$probabilidades[$i]['menosde']   = 0;
					$probabilidades[$i]['masdebg']   = 0;
					$probabilidades[$i]['menosdebg'] = 0;
					$avg_conceded[$i]                = 0;
				}
			}
			
			//calcular corners concedidos promedios de away vs all
			if ($numeropartidos[5] > 0) {
				$avg_conceded[4] = round($conceded[4] / $numeropartidos[5], 2);
			} else {
				$avg_conceded[4] = 0;
			}
			if ($numeropartidos[4] > 0) {
				$avg_conceded[5] = round($conceded[5] / $numeropartidos[4], 2);
			} else {
				$avg_conceded[5] = 0;
			}
			
			$avg_conceded[3] = round(($avg_conceded[4] + $avg_conceded[5]) / 2, 2);
			
			//calcular promedio de probabilidades de todo
			$probabilidades['promedio']['masde']     = round($sumaprobabilidades['masde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['masdebg']   = self::definirBgColor($probabilidades['promedio']['masde']);
			$probabilidades['promedio']['menosde']   = round($sumaprobabilidades['menosde'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['menosdebg'] = self::definirBgColor($probabilidades['promedio']['menosde']);
			
			$resultados                   = array();
			$resultados['cumple']         = $cumple;
			$resultados['probabilidades'] = $probabilidades;
			$resultados['conceded']       = $avg_conceded;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularBTTS($paramref): array
	{
		try {
			$home           = $paramref['home'];
			$away           = $paramref['away'];
			$partidosinfo   = $paramref['partidosinfo'];
			$numeropartidos = $paramref['numeropartidos'];
			
			$numeropartidos_cumple = self::constructArrayRangos();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[0]['yes']++;
					} else {
						$numeropartidos_cumple[0]['no']++;
					}
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[1]['yes']++;
					} else {
						$numeropartidos_cumple[1]['no']++;
					}
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[2]['yes']++;
					} else {
						$numeropartidos_cumple[2]['no']++;
					}
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[3]['yes']++;
					} else {
						$numeropartidos_cumple[3]['no']++;
					}
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[4]['yes']++;
					} else {
						$numeropartidos_cumple[4]['no']++;
					}
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isBTTS($partidoinfo) == 1) {
						$numeropartidos_cumple[5]['yes']++;
					} else {
						$numeropartidos_cumple[5]['no']++;
					}
				}
			}
			
			//calcular probabilidades de cada criterio
			$probabilidades = self::constructArrayRangos();
			
			$sumaprobabilidades                  = array();
			$sumaprobabilidades['yes']           = 0;
			$sumaprobabilidades['no']            = 0;
			$sumaprobabilidades['yes_important'] = 0;
			$sumaprobabilidades['no_important']  = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['yes']   = round(($numeropartidos_cumple[$i]['yes'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['no']    = round(($numeropartidos_cumple[$i]['no'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['yesbg'] = self::definirBgColor($probabilidades[$i]['yes']);
					$probabilidades[$i]['nobg']  = self::definirBgColor($probabilidades[$i]['no']);
					
					$sumaprobabilidades['yes'] += $probabilidades[$i]['yes'];
					$sumaprobabilidades['no']  += $probabilidades[$i]['no'];
				} else {
					$probabilidades[$i]['yes']   = 0;
					$probabilidades[$i]['no']    = 0;
					$probabilidades[$i]['yesbg'] = 0;
					$probabilidades[$i]['nobg']  = 0;
				}
				
				//Sumar solo las probabilidades de home @h y away @a
				if ($i == 1 || $i == 5) {
					if ($probabilidades[$i]['yes'] > 0) {
						$sumaprobabilidades['yes_important'] += $probabilidades[$i]['yes'];
					}
					if ($probabilidades[$i]['no'] > 0) {
						$sumaprobabilidades['no_important'] += $probabilidades[$i]['no'];
					}
				}
				
			}
			
			$probabilidades['promedio']['yes']             = round($sumaprobabilidades['yes'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['yesbg']           = self::definirBgColor($probabilidades['promedio']['yes']);
			$probabilidades['promedio']['no']              = round($sumaprobabilidades['no'] / self::NUMERO_CRITERIOS);
			$probabilidades['promedio']['nobg']            = self::definirBgColor($probabilidades['promedio']['no']);
			$probabilidades['promedio']['yes_important']   = round($sumaprobabilidades['yes_important'] / 2);
			$probabilidades['promedio']['yesbg_important'] = self::definirBgColor($probabilidades['promedio']['yes_important']);
			$probabilidades['promedio']['no_important']    = round($sumaprobabilidades['no_important'] / 2);
			$probabilidades['promedio']['nobg_important']  = self::definirBgColor($probabilidades['promedio']['no_important']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcularMasCorners($paramref): array
	{
		try {
			$home           = $paramref['home'];
			$away           = $paramref['away'];
			$partidosinfo   = $paramref['partidosinfo'];
			$numeropartidos = $paramref['numeropartidos'];
			
			$numeropartidos_cumple = self::constructArrayRangos();
			
			/** @var self[] $partidosinfo */
			foreach ($partidosinfo as $partidoinfo) {
				if (self::isHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersHome($partidoinfo, $home) == 1) {
						$numeropartidos_cumple[0]['home']++;
					}
				}
				if (self::isHomeAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersHome($partidoinfo, $home) == 1) {
						$numeropartidos_cumple[1]['home']++;
					}
				}
				if (self::isHomeAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersHome($partidoinfo, $home) == 1) {
						$numeropartidos_cumple[2]['home']++;
					}
				}
				if (self::isAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersAway($partidoinfo, $away) == 1) {
						$numeropartidos_cumple[3]['away']++;
					}
				}
				if (self::isAwayAtHomevsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersAway($partidoinfo, $away) == 1) {
						$numeropartidos_cumple[4]['away']++;
					}
				}
				if (self::isAwayAtAwayvsAll($partidoinfo, $home, $away) == 1) {
					if (self::isMasCornersAway($partidoinfo, $away) == 1) {
						$numeropartidos_cumple[5]['away']++;
					}
				}
			}
			
			//calcular probabilidades de cada criterio
			$probabilidades = self::constructArrayRangos();
			
			$sumaprobabilidades         = array();
			$sumaprobabilidades['home'] = 0;
			$sumaprobabilidades['away'] = 0;
			
			for ($i = 0; $i < self::NUMERO_CRITERIOS; $i++) {
				if ($numeropartidos[$i] > 0) {
					$probabilidades[$i]['home']   = round(($numeropartidos_cumple[$i]['home'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['away']   = round(($numeropartidos_cumple[$i]['away'] * 100) / $numeropartidos[$i]);
					$probabilidades[$i]['homebg'] = self::definirBgColor($probabilidades[$i]['home']);
					$probabilidades[$i]['awaybg'] = self::definirBgColor($probabilidades[$i]['away']);
					
					$sumaprobabilidades['home'] += $probabilidades[$i]['home'];
					$sumaprobabilidades['away'] += $probabilidades[$i]['away'];
				} else {
					$probabilidades[$i]['home']   = 0;
					$probabilidades[$i]['away']   = 0;
					$probabilidades[$i]['homebg'] = 0;
					$probabilidades[$i]['awaybg'] = 0;
				}
			}
			
			$probabilidades['promedio']['home']   = round($sumaprobabilidades['home'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['homebg'] = self::definirBgColor($probabilidades['promedio']['home']);
			$probabilidades['promedio']['away']   = round($sumaprobabilidades['away'] / (self::NUMERO_CRITERIOS / 2));
			$probabilidades['promedio']['awaybg'] = self::definirBgColor($probabilidades['promedio']['away']);
			
			$resultados                   = array();
			$resultados['cumple']         = $numeropartidos_cumple;
			$resultados['probabilidades'] = $probabilidades;
			
			return $resultados;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_rank_top_n_bottom($rank_size): int|float
	{
		try {
			if ($rank_size <= 15) {
				return 3;
			} elseif ($rank_size > 15 && $rank_size <= 21) {
				return 4;
			} elseif ($rank_size > 21 && $rank_size <= 25) {
				return 5;
			} elseif ($rank_size > 25 && $rank_size <= 35) {
				return 6;
			} elseif ($rank_size > 35 && $rank_size <= 40) {
				return 7;
			} elseif ($rank_size > 40 && $rank_size <= 45) {
				return 8;
			} else {
				return 9;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isMasCornersHome($partidoinfo, $home): int
	{
		try {
			$is = 0;
			
			if ($partidoinfo->home == $home && $partidoinfo->homecorners > $partidoinfo->awaycorners) {
				$is = 1;
			}
			if ($partidoinfo->away == $home && $partidoinfo->homecorners < $partidoinfo->awaycorners) {
				$is = 1;
			}
			
			return $is;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isMasCornersAway($partidoinfo, $away): int
	{
		try {
			$is = 0;
			
			if ($partidoinfo->home == $away && $partidoinfo->homecorners > $partidoinfo->awaycorners) {
				$is = 1;
			}
			if ($partidoinfo->away == $away && $partidoinfo->homecorners < $partidoinfo->awaycorners) {
				$is = 1;
			}
			
			return $is;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isBTTS($partidoinfo): int
	{
		try {
			/** @var self $partidoinfo */
			if ($partidoinfo->homegoals > 0 && $partidoinfo->awaygoals > 0) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isTotalCornersMasDe($partidoinfo, $masde): int
	{
		try {
			if (($partidoinfo->homecorners + $partidoinfo->awaycorners) > $masde) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isTotalCornersMenosDe($partidoinfo, $menosde): int
	{
		try {
			if (($partidoinfo->homecorners + $partidoinfo->awaycorners) < $menosde) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isCornersEquipoMasMenosDe($partidoinfo, $equipo, $masmenosde, $operacion): int
	{
		try {
			$corner_a_comparar = 0;
			
			if ($partidoinfo->home == $equipo) {
				$corner_a_comparar = $partidoinfo->homecorners;
			}
			if ($partidoinfo->away == $equipo) {
				$corner_a_comparar = $partidoinfo->awaycorners;
			}
			
			if ($operacion == 'masde') {
				if ($corner_a_comparar > $masmenosde) {
					return 1;
				} else {
					return 0;
				}
			} else {
				if ($corner_a_comparar < $masmenosde) {
					return 1;
				} else {
					return 0;
				}
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isgoles_equipo_masmenosde($partidoinfo, $equipo, $masmenosde, $operacion): int
	{
		try {
			/** @var self $partidoinfo */
			
			$goles_a_comparar = 0;
			
			if ($partidoinfo->home == $equipo) {
				$goles_a_comparar = $partidoinfo->homegoals;
			}
			if ($partidoinfo->away == $equipo) {
				$goles_a_comparar = $partidoinfo->awaygoals;
			}
			
			if ($operacion == 'masde') {
				if ($goles_a_comparar > $masmenosde) {
					return 1;
				} else {
					return 0;
				}
			} else {
				if ($goles_a_comparar < $masmenosde) {
					return 1;
				} else {
					return 0;
				}
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isTotalGolesMasDe($partidoinfo, $masde): int
	{
		try {
			/** @var self $partidoinfo */
			if (($partidoinfo->homegoals + $partidoinfo->awaygoals) > $masde) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isTotalGolesMenosDe($partidoinfo, $menosde): int
	{
		try {
			if (($partidoinfo->homegoals + $partidoinfo->awaygoals) < $menosde) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_home_wins($partidoinfo, $home): int
	{
		try {
			if (($partidoinfo->home == $home && $partidoinfo->homegoals > $partidoinfo->awaygoals) || ($partidoinfo->away == $home && $partidoinfo->homegoals < $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_home_wins_athome($partidoinfo, $home): int
	{
		try {
			if (($partidoinfo->home == $home && $partidoinfo->homegoals > $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_home_wins_ataway($partidoinfo, $home): int
	{
		try {
			if (($partidoinfo->away == $home && $partidoinfo->homegoals < $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_away_wins($partidoinfo, $away): int
	{
		try {
			if (($partidoinfo->home == $away && $partidoinfo->homegoals > $partidoinfo->awaygoals) || ($partidoinfo->away == $away && $partidoinfo->homegoals < $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_away_wins_athome($partidoinfo, $away): int
	{
		try {
			if (($partidoinfo->home == $away && $partidoinfo->homegoals > $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function is_away_wins_ataway($partidoinfo, $away): int
	{
		try {
			if (($partidoinfo->away == $away && $partidoinfo->homegoals < $partidoinfo->awaygoals)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isSameFixture($partidoinfo, $home, $away): int
	{
		try {
			if (($partidoinfo->home == $home && $partidoinfo->away == $away) || ($partidoinfo->away == $home && $partidoinfo->home == $away)) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isHomeAtHomevsAway($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $home && $partidoinfo->away == $away) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isHomeAtAwayvsAway($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $away && $partidoinfo->away == $home) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isHomevsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $home || $partidoinfo->away == $home) {
				return 1;
			} else {
				return 0;
			}
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isHomeAtHomevsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $home) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isHomeAtAwayvsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->away == $home) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isAwayvsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $away || $partidoinfo->away == $away) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isAwayAtHomevsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->home == $away) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function isAwayAtAwayvsAll($partidoinfo, $home, $away): int
	{
		try {
			if ($partidoinfo->away == $away) {
				return 1;
			} else {
				return 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	public static function definirBgColor($porcentaje): string
	{
		if ($porcentaje >= self::VALUETOGREENSTRONG) {
			return self::BGTOGREENSTRONG;
			
		} elseif ($porcentaje > self::VALUETOGREEN && $porcentaje < self::VALUETOGREENSTRONG) {
			return self::BGTOGREEN;
			
		} elseif ($porcentaje > self::VALUETOYELLOW && $porcentaje <= self::VALUETOGREEN) {
			return self::BGTOYELLOW;
			
		} else {
			return 'bg-silver-600';
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function definir_cumple_ultimospartidos_bgcolor($paramref, PDO $conexion): array
	{
		try {
			/** @var self[] $last_partidosinfo */
			
			$idapuestatipo          = $paramref['idapuestatipo'];
			$valorapuesta           = $paramref['valorapuesta'];
			$valorapuesta_acomparar = $valorapuesta;
			$last_partidosinfo      = $paramref['last_partidosinfo'];
			$equipo                 = $paramref['equipo'];
			$is_inverse             = $paramref['is_inverse'];
			
			$nocumple_bgcolor = array();
			$penalidades      = array();
			$current_config   = Config::get($conexion);
			$n_cumple         = 0;
			$n_todos          = count($last_partidosinfo);
			$total_penalidad  = 0;
			$falla_1ro        = 0;
			$falla_2do        = 0;
			$falla_3ro        = 0;
			$falla_mitad      = 0;
			$falla_resto      = 0;
			$falla_both       = 0;
			$i                = 1;
			
			foreach ($last_partidosinfo as $last_partidoinfo) {
				if ($is_inverse == 1) {
					if ($equipo == 'home') {
						$homecorners = $last_partidoinfo->homecorners;
						$homegoals   = $last_partidoinfo->homegoals;
						$awaycorners = $last_partidoinfo->homecorners;
						$awaygoals   = $last_partidoinfo->homegoals;
						
					} elseif ($equipo == 'away') {
						$homecorners = $last_partidoinfo->awaycorners;
						$homegoals   = $last_partidoinfo->awaygoals;
						$awaycorners = $last_partidoinfo->awaycorners;
						$awaygoals   = $last_partidoinfo->awaygoals;
						
					} else {
						$homecorners = $last_partidoinfo->homecorners;
						$homegoals   = $last_partidoinfo->homegoals;
						$awaycorners = $last_partidoinfo->awaycorners;
						$awaygoals   = $last_partidoinfo->awaygoals;
					}
				} else {
					$homecorners = $last_partidoinfo->homecorners;
					$homegoals   = $last_partidoinfo->homegoals;
					$awaycorners = $last_partidoinfo->awaycorners;
					$awaygoals   = $last_partidoinfo->awaygoals;
				}
				
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MENOSXTOTALCORNERS) {
					$resultado          = self::definir_cumple_ultimospartidos($last_partidoinfo->corners_total, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MASXTOTALCORNERS) {
					$resultado          = self::definir_cumple_ultimospartidos($last_partidoinfo->corners_total, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MENOSXCORNERSHOME) {
					$resultado          = self::definir_cumple_ultimospartidos($homecorners, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MASXCORNERSHOME) {
					$resultado          = self::definir_cumple_ultimospartidos($homecorners, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MENOSXCORNERSAWAY) {
					$resultado          = self::definir_cumple_ultimospartidos($awaycorners, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_MASXCORNERSAWAY) {
					$resultado          = self::definir_cumple_ultimospartidos($awaycorners, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_TOTAL_GOLES_MENOSDE) {
					$resultado          = self::definir_cumple_ultimospartidos($last_partidoinfo->goals_total, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_TOTAL_GOLES_MASDE) {
					$resultado          = self::definir_cumple_ultimospartidos($last_partidoinfo->goals_total, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_GOLES_HOME_MENOSDE) {
					$resultado          = self::definir_cumple_ultimospartidos($homegoals, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_GOLES_HOME_MASDE) {
					$resultado          = self::definir_cumple_ultimospartidos($homegoals, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_GOLES_AWAY_MENOSDE) {
					$resultado          = self::definir_cumple_ultimospartidos($awaygoals, $valorapuesta_acomparar, 'menosde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				if (ordena($idapuestatipo) == ApuestaTipo::ID_GOLES_AWAY_MASDE) {
					$resultado          = self::definir_cumple_ultimospartidos($awaygoals, $valorapuesta_acomparar, 'masde');
					$nocumple_bgcolor[] = $resultado;
					
					self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
				}
				/*if(ordena($idapuestatipo) == ApuestaTipo::ID_HOME_WINS) {
                    $resultado = self::definir_cumple_team_wins_ultimospartidos($last_partidoinfo, 'home_wins');
                    $nocumple_bgcolor[] = $resultado;

                    self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
                }
                if(ordena($idapuestatipo) == ApuestaTipo::ID_AWAY_WINS) {
                    $resultado = self::definir_cumple_team_wins_ultimospartidos($last_partidoinfo, 'away_wins');
                    $nocumple_bgcolor[] = $resultado;

                    self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
                }
                if(ordena($idapuestatipo) == ApuestaTipo::ID_BTTS_YES) {
                    $resultado = self::definir_cumple_btts_ultimospartidos($last_partidoinfo, 'yes');
                    $nocumple_bgcolor[] = $resultado;

                    self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
                }
                if(ordena($idapuestatipo) == ApuestaTipo::ID_BTTS_NO) {
                    $resultado = self::definir_cumple_btts_ultimospartidos($last_partidoinfo, 'no');
                    $nocumple_bgcolor[] = $resultado;

                    self::calcular_valores_cumplimiento($equipo, $i, $resultado, $current_config, $penalidades, $total_penalidad, $n_cumple, $n_todos, $falla_1ro, $falla_2do, $falla_3ro, $falla_mitad, $falla_resto, $falla_both);
                }*/
				
				$i++;
			}
			
			$resultado                     = array();
			$resultado['nocumple_bgcolor'] = $nocumple_bgcolor;
			$resultado['percentage_exito'] = self::calcular_percentage_exito($n_cumple, $n_todos);
			$resultado['penalidades']      = $penalidades;
			$resultado['total_penalidad']  = $total_penalidad;
			$resultado['falla_1ro']        = $falla_1ro;
			$resultado['falla_2do']        = $falla_2do;
			$resultado['falla_3ro']        = $falla_3ro;
			$resultado['falla_mitad']      = $falla_mitad;
			$resultado['falla_resto']      = $falla_resto;
			$resultado['falla_both']       = $falla_both;
			
			return $resultado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @param        $equipo
	 * @param        $i
	 * @param        $resultado
	 * @param Config $current_config
	 * @param        $penalidades
	 * @param        $total_penalidad
	 * @param        $n_cumple
	 * @param        $n_todos
	 * @param        $falla_1ro
	 * @param        $falla_2do
	 * @param        $falla_3ro
	 * @param        $falla_mitad
	 * @param        $falla_resto
	 * @param        $falla_both
	 *
	 * @throws Exception
	 */
	public static function calcular_valores_cumplimiento($equipo, $i, $resultado, Config $current_config, &$penalidades, &$total_penalidad, &$n_cumple, $n_todos, &$falla_1ro, &$falla_2do, &$falla_3ro, &$falla_mitad, &$falla_resto, &$falla_both): void
	{
		try {
			$val_penalidad_mitad        = $current_config->val_penalidad_mitad_ultimos_partidos;
			$val_penalidad_resto        = $current_config->val_penalidad_resto_ultimos_partidos;
			$val_penalidad_same_fixture = $current_config->val_penalidad_same_fixture;
			$val_penalidad_1ro          = $current_config->val_penalidad_1ro_ultimos_partidos;
			$val_penalidad_2do          = $current_config->val_penalidad_2do_ultimos_partidos;
			$val_penalidad_3ro          = $current_config->val_penalidad_3ro_ultimos_partidos;
			
			//calcular penalidad
			if ($equipo != 'both') {
				$val_penalidad = match ($i) {
					1       => $val_penalidad_1ro,
					2       => $val_penalidad_2do,
					3       => $val_penalidad_3ro,
					default => ($i < ($n_todos / 2)) ? $val_penalidad_mitad : $val_penalidad_resto,
				};
			} else { //penalidad exclusive para same fixture
				$val_penalidad = $val_penalidad_same_fixture;
			}
			if (!empty($resultado)) { //no cumple
				$penalidades[]   = $val_penalidad;
				$total_penalidad += $val_penalidad;
				
				if ($equipo != 'both') { //sumar fallas dependiendo de posicion para home y away
					if ($i == 1) {
						$falla_1ro = 1;
					}
					if ($i == 2) {
						$falla_2do = 1;
					}
					if ($i == 3) {
						$falla_3ro = 1;
					}
					if ($i > 3 && $i <= 6) {
						$falla_mitad += 1;
					}
					if ($i > 6) {
						$falla_resto += 1;
					}
				} else { //suma fallas para same fixture
					$falla_both += 1;
				}
			} else { //cumple
				$n_cumple      += 1;
				$penalidades[] = 0;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function calcular_percentage_exito($n_cumple, $n_todos): int|float
	{
		try {
			if ($n_todos > 0) {
				return round(($n_cumple * 100) / $n_todos);
			} else {
				return 0;
			}
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function definir_cumple_ultimospartidos($valor_acomparar, $valor_apuesta, $operacion): string
	{
		try {
			$nocumple_bgcolor = '';
			
			if ($operacion == 'menosde') {
				if ($valor_acomparar >= $valor_apuesta) {
					$nocumple_bgcolor = 'bg-danger';
				}
			} else {
				if ($valor_acomparar <= $valor_apuesta) {
					$nocumple_bgcolor = 'bg-danger';
				}
			}
			
			return $nocumple_bgcolor;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function definir_cumple_team_wins_ultimospartidos($last_partidoinfo, $operacion): string
	{
		try {
			/** @var self $last_partidoinfo */
			
			$nocumple_bgcolor = '';
			
			if ($operacion == 'home_wins') {
				if ($last_partidoinfo->homegoals <= $last_partidoinfo->awaygoals) {
					$nocumple_bgcolor = 'bg-danger';
				}
			} else {
				if ($last_partidoinfo->homegoals >= $last_partidoinfo->awaygoals) {
					$nocumple_bgcolor = 'bg-danger';
				}
			}
			
			return $nocumple_bgcolor;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function definir_cumple_btts_ultimospartidos($last_partidoinfo, $operacion): string
	{
		try {
			/** @var self $last_partidoinfo */
			
			$nocumple_bgcolor = '';
			
			if ($operacion == 'yes') {
				if ($last_partidoinfo->homegoals == 0 || $last_partidoinfo->awaygoals == 0) {
					$nocumple_bgcolor = 'bg-danger';
				}
			} else {
				if ($last_partidoinfo->homegoals > 0 && $last_partidoinfo->awaygoals > 0) {
					$nocumple_bgcolor = 'bg-danger';
				}
			}
			
			return $nocumple_bgcolor;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_posession($paramref, $conexion): array
	{
		try {
			$rank_vsall  = self::get_rank_posession($paramref, 1, 0, $conexion);
			$rank_athome = self::get_rank_posession($paramref, 0, 1, $conexion);
			$rank_ataway = self::get_rank_posession($paramref, 0, 0, $conexion);
			
			$posession    = array();
			$posession[0] = $rank_vsall['rankhome_podio'];
			$posession[1] = $rank_athome['rankhome_podio'];
			$posession[2] = $rank_ataway['rankhome_podio'];
			$posession[3] = $rank_vsall['rankaway_podio'];
			$posession[4] = $rank_athome['rankaway_podio'];
			$posession[5] = $rank_ataway['rankaway_podio'];
			
			return $posession;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_shots($paramref, $conexion): array
	{
		try {
			$rank_vsall  = self::get_rank_shots($paramref, 1, 0, $conexion);
			$rank_athome = self::get_rank_shots($paramref, 0, 1, $conexion);
			$rank_ataway = self::get_rank_shots($paramref, 0, 0, $conexion);
			
			$shots    = array();
			$shots[0] = $rank_vsall['rankhome_podio'];
			$shots[1] = $rank_athome['rankhome_podio'];
			$shots[2] = $rank_ataway['rankhome_podio'];
			$shots[3] = $rank_vsall['rankaway_podio'];
			$shots[4] = $rank_athome['rankaway_podio'];
			$shots[5] = $rank_ataway['rankaway_podio'];
			
			return $shots;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_shotstarget($paramref, $conexion): array
	{
		try {
			$rank_vsall  = self::get_rank_shotstarget($paramref, 1, 0, $conexion);
			$rank_athome = self::get_rank_shotstarget($paramref, 0, 1, $conexion);
			$rank_ataway = self::get_rank_shotstarget($paramref, 0, 0, $conexion);
			
			$shotstarget    = array();
			$shotstarget[0] = $rank_vsall['rankhome_podio'];
			$shotstarget[1] = $rank_athome['rankhome_podio'];
			$shotstarget[2] = $rank_ataway['rankhome_podio'];
			$shotstarget[3] = $rank_vsall['rankaway_podio'];
			$shotstarget[4] = $rank_athome['rankaway_podio'];
			$shotstarget[5] = $rank_ataway['rankaway_podio'];
			
			return $shotstarget;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_corners($paramref, $conexion): array
	{
		try {
			$rank_vsall  = self::get_rank_corners($paramref, 1, 0, $conexion);
			$rank_athome = self::get_rank_corners($paramref, 0, 1, $conexion);
			$rank_ataway = self::get_rank_corners($paramref, 0, 0, $conexion);
			
			$corners    = array();
			$corners[0] = $rank_vsall['rankhome_podio'];
			$corners[1] = $rank_athome['rankhome_podio'];
			$corners[2] = $rank_ataway['rankhome_podio'];
			$corners[3] = $rank_vsall['rankaway_podio'];
			$corners[4] = $rank_athome['rankaway_podio'];
			$corners[5] = $rank_ataway['rankaway_podio'];
			
			return $corners;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_rank_goals($paramref, $conexion): array
	{
		try {
			$rank_vsall  = self::get_rank_goals($paramref, 1, 0, $conexion);
			$rank_athome = self::get_rank_goals($paramref, 0, 1, $conexion);
			$rank_ataway = self::get_rank_goals($paramref, 0, 0, $conexion);
			
			$goals    = array();
			$goals[0] = $rank_vsall['rankhome_podio'];
			$goals[1] = $rank_athome['rankhome_podio'];
			$goals[2] = $rank_ataway['rankhome_podio'];
			$goals[3] = $rank_vsall['rankaway_podio'];
			$goals[4] = $rank_athome['rankaway_podio'];
			$goals[5] = $rank_ataway['rankaway_podio'];
			
			return $goals;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_probabilities($paramref, $conexion): array
	{
		try {
			$modpartido           = $paramref['modpartido'];
			$partidotorneos       = $paramref['partidotorneos'];
			$totalcorners_masde   = $paramref['totalcorners_masde'];
			$totalcorners_menosde = $paramref['totalcorners_menosde'];
			$totalgoles_masde     = $paramref['totalgoles_masde'];
			$totalgoles_menosde   = $paramref['totalgoles_menosde'];
			$cornershome_masde    = $paramref['cornershome_masde'];
			$cornershome_menosde  = $paramref['cornershome_menosde'];
			$cornersaway_masde    = $paramref['cornersaway_masde'];
			$cornersaway_menosde  = $paramref['cornersaway_menosde'];
			$goleshome_masde      = $paramref['goleshome_masde'];
			$goleshome_menosde    = $paramref['goleshome_menosde'];
			$golesaway_masde      = $paramref['golesaway_masde'];
			$golesaway_menosde    = $paramref['golesaway_menosde'];
			
			$resultado = array();
			
			//empezar a crear tabla de probabilidades
			$param                       = array();
			$param['home']               = $modpartido->home;
			$param['away']               = $modpartido->away;
			$resultado['criterios']      = self::getCriterios($param);
			$resultado['criterios_hvsa'] = self::get_criterios_hvsa();
			
			$param['pais']               = $modpartido->pais;
			$param['paises_adicionales'] = $partidotorneos;
			$param['partidosinfo']       = self::getList($param, $conexion);
			$numeropartidos              = self::calcularNumeroPartidos($param);
			
			#region region corners
			$param['numeropartidos']       = $numeropartidos;
			$param['totalcorners_masde']   = $totalcorners_masde;
			$param['totalcorners_menosde'] = $totalcorners_menosde;
			$resultado['infototalcorners'] = self::calcularProbabilidadTotalCorners($param);
			
			$param['cornershome_masde']   = $cornershome_masde;
			$param['cornershome_menosde'] = $cornershome_menosde;
			$resultado['infocornershome'] = self::calcularProbabilidadCornersHome($param);
			
			$param['cornersaway_masde']   = $cornersaway_masde;
			$param['cornersaway_menosde'] = $cornersaway_menosde;
			$resultado['infocornersaway'] = self::calcularProbabilidadCornersAway($param);
			#endregion corners
			#region region goles
			$param['totalgoles_masde']   = $totalgoles_masde;
			$param['totalgoles_menosde'] = $totalgoles_menosde;
			$resultado['infototalgoles'] = self::calcularProbabilidadTotalGoles($param);
			
			$param['goleshome_masde']    = $goleshome_masde;
			$param['goleshome_menosde']  = $goleshome_menosde;
			$resultado['info_goleshome'] = self::calcular_probabilidades_goleshome($param);
			
			$param['golesaway_masde']    = $golesaway_masde;
			$param['golesaway_menosde']  = $golesaway_menosde;
			$resultado['info_golesaway'] = self::calcular_probabilidades_golesaway($param);
			#endregion goles
			
			$resultado['promediocorners'] = self::calcularPromedioCorners($param);
			$resultado['promediogoles']   = self::calcular_promedio_goles($param);
			
			$resultado['numeropartidos'] = $numeropartidos;
			
			//rankings
			$param['pais']            = $modpartido->pais;
			$resultado['posession']   = self::create_table_posession($param, $conexion);
			$resultado['shots']       = self::create_table_shots($param, $conexion);
			$resultado['shotstarget'] = self::create_table_shotstarget($param, $conexion);
			$resultado['corners']     = self::create_table_corners($param, $conexion);
			$resultado['rank_goals']  = self::create_table_rank_goals($param, $conexion);
			
			return $resultado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function create_table_ultimos_partidos($modpartido, $id_apuesta_tipo, $valor_apuesta, PDO $conexion): array
	{
		try {
			$param                      = array();
			$param['home_search_equal'] = $modpartido->home;
			$param['orderby_fecha']     = 1;
			$param['limit_12']          = 1;
			$last_partidosinfo_home     = PartidoInfo::getList($param, $conexion);
			
			$param                      = array();
			$param['away_search_equal'] = $modpartido->away;
			$param['orderby_fecha']     = 1;
			$param['limit_12']          = 1;
			$last_partidosinfo_away     = PartidoInfo::getList($param, $conexion);
			
			$param                      = array();
			$param['home_search_equal'] = $modpartido->home;
			$param['away_search_equal'] = $modpartido->away;
			$param['orderby_fecha']     = 1;
			$param['limit_12']          = 1;
			$last_partidosinfo_both     = PartidoInfo::getList($param, $conexion);
			
			//resaltar partidos que no cumplen con la apuesta.
			$nocumple_ultimospartidos_home = PartidoInfo::construct_array($last_partidosinfo_home);
			$nocumple_ultimospartidos_away = PartidoInfo::construct_array($last_partidosinfo_away);
			$nocumple_ultimospartidos_both = PartidoInfo::construct_array($last_partidosinfo_both);
			
			$param                  = array();
			$param['idapuestatipo'] = $id_apuesta_tipo;
			$param['valorapuesta']  = $valor_apuesta;
			
			$param['last_partidosinfo']    = $last_partidosinfo_home;
			$param['equipo']               = 'home';
			$param['is_inverse']           = 0;
			$resultado_home                = Partidoinfo::definir_cumple_ultimospartidos_bgcolor($param, $conexion);
			$nocumple_ultimospartidos_home = $resultado_home['nocumple_bgcolor'];
			$percentage_exito_home         = $resultado_home['percentage_exito'];
			$penalidades_home              = $resultado_home['penalidades'];
			$total_penalidad_home          = $resultado_home['total_penalidad'];
			
			$param['last_partidosinfo']    = $last_partidosinfo_away;
			$param['equipo']               = 'away';
			$param['is_inverse']           = 0;
			$resultado_away                = Partidoinfo::definir_cumple_ultimospartidos_bgcolor($param, $conexion);
			$nocumple_ultimospartidos_away = $resultado_away['nocumple_bgcolor'];
			$percentage_exito_away         = $resultado_away['percentage_exito'];
			$penalidades_away              = $resultado_away['penalidades'];
			$total_penalidad_away          = $resultado_away['total_penalidad'];
			
			$param['last_partidosinfo']    = $last_partidosinfo_both;
			$param['equipo']               = 'both';
			$param['is_inverse']           = 0;
			$resultado_both                = Partidoinfo::definir_cumple_ultimospartidos_bgcolor($param, $conexion);
			$nocumple_ultimospartidos_both = $resultado_both['nocumple_bgcolor'];
			$percentage_exito_both         = $resultado_both['percentage_exito'];
			$penalidades_both              = $resultado_both['penalidades'];
			$total_penalidad_both          = $resultado_both['total_penalidad'];
			
			//background color according to criteria:
			$percentage_exito_bgcolor = '';
			
			if ($percentage_exito_home >= 60 && $percentage_exito_away >= 60) {
				$percentage_exito_bgcolor = 'bg-success';
				$percentage_exito_valido  = 1;
			} else {
				$percentage_exito_bgcolor = 'bg-danger';
				$percentage_exito_valido  = 0;
			}
			
			#region region return
			$resultado                                  = array();
			$resultado['resultado_home']                = $resultado_home;
			$resultado['resultado_away']                = $resultado_away;
			$resultado['resultado_both']                = $resultado_both;
			$resultado['nocumple_ultimospartidos_home'] = $nocumple_ultimospartidos_home;
			$resultado['nocumple_ultimospartidos_away'] = $nocumple_ultimospartidos_away;
			$resultado['nocumple_ultimospartidos_both'] = $nocumple_ultimospartidos_both;
			$resultado['percentage_exito_home']         = $percentage_exito_home;
			$resultado['percentage_exito_away']         = $percentage_exito_away;
			$resultado['percentage_exito_both']         = $percentage_exito_both;
			$resultado['percentage_exito_bgcolor']      = $percentage_exito_bgcolor;
			$resultado['percentage_exito_valido']       = $percentage_exito_valido;
			$resultado['penalidades_home']              = $penalidades_home;
			$resultado['penalidades_away']              = $penalidades_away;
			$resultado['penalidades_both']              = $penalidades_both;
			$resultado['total_penalidad_home']          = $total_penalidad_home;
			$resultado['total_penalidad_away']          = $total_penalidad_away;
			$resultado['total_penalidad_both']          = $total_penalidad_both;
			$resultado['last_partidosinfo_home']        = $last_partidosinfo_home;
			$resultado['last_partidosinfo_away']        = $last_partidosinfo_away;
			$resultado['last_partidosinfo_both']        = $last_partidosinfo_both;
			#endregion return
			
			return $resultado;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validateData($conexion): void
	{
		try {
			$this->status = mb_strtoupper($this->status);
			$this->home   = $this->home;
			$this->away   = $this->away;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function validateDataDeleteSelected($idpais, $season): void
	{
		try {
			validar_textovacio($idpais, "Debe especificar el pais");
			validar_textovacio($season, "Debe especificar el season");
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function validate_data_corregirteam($paramref): void
	{
		try {
			validar_textovacio($paramref['pais'], "Debe especificar el pais");
			validar_textovacio($paramref['team_acorregir'], 'Debe especificar el team a corregir');
			validar_textovacio($paramref['team_corregido'], 'Debe especificar el team corregido');
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>